<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fixed Flexbox Test</title>
    <style>
        /* Fixed flexbox layout - no flex-wrap */
        .container {
            display: flex;
            gap: 30px;
            margin-bottom: 20px;
            border: 1px solid blue;
            padding: 10px;
        }
        .text {
            flex: 1 1 60%;
            min-width: 0;
            order: 1;
            background-color: #f0f0f0;
            padding: 10px;
        }
        .image {
            flex: 0 0 40%;
            align-self: flex-start;
            order: 2;
            background-color: #e0e0e0;
            padding: 10px;
        }

        /* Media query for mobile devices */
        @media (max-width: 767.98px) {
            .container {
                flex-direction: column;
                gap: 15px;
            }
            .text,
            .image {
                flex: 0 0 100%;
                width: 100%;
                max-width: 100%;
            }
        }
    </style>
</head>
<body>
    <h1>Fixed Flexbox Layout</h1>

    <div class="container">
        <div class="text">
            <h3>Text Content</h3>
            <p>This is the text content that should appear on the left side of the container on desktop.</p>
            <p>The key fix was removing flex-wrap: wrap from the container, which was causing the layout to break.</p>
        </div>
        <div class="image">
            <h3>Image Content</h3>
            <p>This represents an image that should appear on the right side of the container on desktop.</p>
        </div>
    </div>
</body>
</html>
