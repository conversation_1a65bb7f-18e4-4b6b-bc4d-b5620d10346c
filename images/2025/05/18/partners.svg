<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg width="100%" height="100%" viewBox="0 0 837 186" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" xmlns:serif="http://www.serif.com/" style="fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2;">
    <g transform="matrix(0.122638,0,0,0.131251,688.046,89.9105)">
        <g transform="matrix(1,0,0,1,-1216,-618.667)">
            <clipPath id="_clip1">
                <rect x="0" y="0" width="2432" height="1237.33"/>
            </clipPath>
            <g clip-path="url(#_clip1)">
                <g transform="matrix(0.266667,0,0,-0.266667,-2.47584,1234.95)">
                    <path d="M5230,4630L5230,4520L5600,4520L5600,3400L5710,3400L5712,3958L5715,4515L5898,4518L6080,4520L6080,4630L5230,4630Z" style="fill:white;fill-rule:nonzero;"/>
                    <path d="M6347,4623C6343,4620 6340,4343 6340,4008L6340,3400L6450,3400L6452,3678L6455,3955L7085,3955L7088,3678L7090,3400L7200,3400L7198,4013L7195,4625L7143,4628L7090,4631L7090,4070L6450,4070L6450,4630L6402,4630C6375,4630 6350,4627 6347,4623Z" style="fill:white;fill-rule:nonzero;"/>
                    <path d="M7750,4620C7641,4606 7535,4526 7487,4422C7466,4377 7465,4363 7465,4015L7465,3655L7498,3588C7534,3515 7584,3465 7659,3428C7704,3406 7715,3405 8013,3402L8320,3399L8320,3510L8038,3510C7775,3510 7752,3512 7710,3531C7654,3556 7624,3584 7597,3635C7579,3669 7575,3697 7572,3818L7568,3960L8130,3960L8130,4070L7570,4070L7570,4188C7570,4370 7600,4437 7704,4493C7743,4513 7761,4515 8033,4518L8321,4522L8318,4573L8315,4625L8060,4626C7920,4627 7780,4624 7750,4620Z" style="fill:white;fill-rule:nonzero;"/>
                    <path d="M253,2926C165,2898 77,2819 37,2732C16,2686 15,2673 12,2193L9,1700L180,1700L182,1878L185,2055L715,2055L718,1878L720,1700L890,1700L890,2675L861,2735C826,2808 763,2872 690,2908C637,2934 627,2935 465,2937C345,2939 283,2935 253,2926ZM605,2751C652,2729 694,2687 709,2647C716,2630 720,2537 720,2424L720,2230L180,2230L180,2413C180,2513 185,2612 191,2633C205,2684 255,2737 305,2755C367,2777 554,2774 605,2751Z" style="fill:white;fill-rule:nonzero;"/>
                    <path d="M1182,2453L1185,1965L1212,1908C1246,1835 1315,1766 1388,1732C1445,1705 1446,1705 1713,1702L1981,1699L1975,1875L1463,1885L1428,1912C1354,1968 1355,1960 1352,2478L1349,2940L1179,2940L1182,2453Z" style="fill:white;fill-rule:nonzero;"/>
                    <path d="M1900,2940L1900,2770L2260,2770L2260,1700L2430,1700L2432,2233L2435,2765L2613,2768L2790,2770L2790,2940L1900,2940Z" style="fill:white;fill-rule:nonzero;"/>
                    <path d="M3070,2940L3070,2770L3250,2770L3250,1881L3163,1878L3075,1875L3072,1788L3069,1700L3611,1700L3608,1788L3605,1875L3518,1878L3430,1881L3430,2770L3610,2770L3610,2940L3070,2940Z" style="fill:white;fill-rule:nonzero;"/>
                    <path d="M3890,2940L3890,2770L4250,2770L4250,1700L4430,1700L4430,2770L4780,2770L4780,2940L3890,2940Z" style="fill:white;fill-rule:nonzero;"/>
                    <path d="M5072,2448L5075,1955L5100,1905C5131,1841 5211,1761 5275,1730C5323,1706 5334,1705 5510,1705C5695,1705 5695,1705 5753,1734C5818,1767 5886,1835 5922,1901L5945,1945L5948,2443L5951,2940L5780,2940L5780,2481C5780,2180 5776,2012 5769,1993C5756,1959 5714,1913 5678,1894C5639,1874 5380,1874 5341,1895C5302,1915 5263,1964 5250,2008C5244,2031 5240,2216 5240,2493L5240,2940L5069,2940L5072,2448Z" style="fill:white;fill-rule:nonzero;"/>
                    <path d="M6240,2941L6240,1699L6553,1702L6865,1705L6922,1732C6995,1766 7064,1835 7098,1908L7125,1965L7125,2685L7091,2748C7053,2819 6992,2877 6922,2912C6876,2934 6865,2935 6558,2938L6240,2941ZM6822,2760C6870,2746 6919,2705 6936,2663C6947,2638 6950,2560 6950,2319C6950,1972 6948,1963 6882,1912L6847,1885L6633,1882L6420,1879L6420,2770L6603,2770C6703,2770 6802,2765 6822,2760Z" style="fill:white;fill-rule:nonzero;"/>
                    <path d="M7638,2916C7544,2877 7489,2823 7443,2723C7416,2665 7416,2664 7412,2383C7407,2078 7416,1966 7451,1899C7483,1836 7556,1763 7619,1733L7675,1705L7988,1702L8300,1699L8300,1880L8010,1880C7695,1880 7691,1881 7638,1937C7598,1979 7590,2011 7590,2131L7590,2240L8120,2240L8120,2410L7590,2410L7590,2515C7590,2635 7601,2669 7651,2716C7706,2767 7729,2770 8026,2770L8300,2770L8300,2940L7998,2940C7704,2940 7693,2939 7638,2916Z" style="fill:white;fill-rule:nonzero;"/>
                    <path d="M2170,1221C2060,1205 1954,1125 1907,1022C1886,977 1885,963 1885,615L1885,255L1918,188C1954,115 2004,65 2079,28C2122,6 2137,5 2310,5C2493,5 2496,5 2548,33C2662,93 2722,187 2728,312L2732,390L2622,390L2618,320C2613,232 2579,179 2501,138C2451,112 2441,110 2318,110C2246,110 2170,115 2149,121C2099,137 2044,182 2017,233C1996,273 1995,287 1992,588C1988,945 1993,980 2056,1042C2120,1107 2147,1115 2310,1115C2432,1115 2461,1112 2490,1096C2575,1051 2620,982 2620,895L2620,840L2730,840L2730,908C2730,1031 2655,1141 2535,1196C2490,1217 2463,1221 2353,1225C2283,1227 2200,1225 2170,1221Z" style="fill:white;fill-rule:nonzero;"/>
                    <path d="M3278,1219C3172,1204 3078,1134 3029,1035L3005,985L3005,235L3032,185C3064,123 3135,56 3198,27C3242,6 3262,5 3548,2L3850,-1L3850,110L3568,110C3256,110 3239,113 3176,176C3121,231 3110,271 3110,423L3110,560L3660,560L3660,670L3110,670L3110,805C3110,896 3114,951 3123,973C3145,1024 3190,1071 3240,1094C3282,1113 3306,1115 3568,1118L3850,1122L3850,1230L3593,1229C3451,1228 3309,1224 3278,1219Z" style="fill:white;fill-rule:nonzero;"/>
                    <path d="M4400,1221C4290,1205 4184,1125 4137,1022C4116,976 4115,963 4112,488L4109,0L4220,0L4220,453C4220,982 4220,981 4301,1054C4362,1109 4387,1115 4545,1115C4680,1115 4724,1104 4777,1057C4851,992 4850,1001 4850,473L4850,0L4960,0L4960,977L4930,1038C4894,1109 4838,1163 4765,1196C4720,1217 4693,1221 4583,1225C4513,1227 4430,1225 4400,1221Z" style="fill:white;fill-rule:nonzero;"/>
                    <path d="M5230,1230L5230,1120L5600,1120L5600,0L5710,0L5712,558L5715,1115L5898,1118L6080,1120L6080,1230L5230,1230Z" style="fill:white;fill-rule:nonzero;"/>
                    <path d="M6627,1219C6515,1203 6423,1131 6371,1021L6345,965L6342,483L6338,0L6450,0L6450,370L6680,370C6931,370 6957,365 7013,312C7066,262 7083,215 7088,103L7093,0L7203,0L7197,113C7191,257 7173,309 7105,377L7053,429L7091,459C7114,478 7142,516 7162,557L7195,625L7195,800C7195,959 7193,979 7173,1022C7145,1084 7072,1160 7015,1189C6939,1227 6771,1240 6627,1219ZM6953,1095C7011,1068 7059,1012 7076,952C7088,910 7091,866 7088,770C7085,649 7083,640 7054,594C7036,564 7006,534 6976,515L6927,485L6455,485L6455,715C6456,919 6458,949 6474,980C6512,1053 6565,1093 6643,1110C6710,1125 6912,1115 6953,1095Z" style="fill:white;fill-rule:nonzero;"/>
                    <path d="M7750,1220C7641,1206 7535,1126 7487,1022C7466,977 7465,963 7465,615L7465,255L7498,188C7534,115 7584,65 7659,28C7704,6 7715,5 8013,2L8320,-1L8320,110L8038,110C7775,110 7752,112 7710,131C7654,156 7624,184 7597,235C7579,269 7575,297 7572,418L7568,560L8130,560L8130,670L7570,670L7570,788C7570,970 7600,1037 7704,1093C7743,1113 7761,1115 8033,1118L8321,1122L8318,1173L8315,1225L8060,1226C7920,1227 7780,1224 7750,1220Z" style="fill:white;fill-rule:nonzero;"/>
                    <g transform="matrix(1,0,0,1,0,3396.19)">
                        <path d="M8450,1210C8450,1194 8458,1191 8508,1188L8565,1185L8568,1028C8570,879 8572,870 8590,870C8608,870 8610,879 8612,1028L8615,1185L8673,1188C8722,1191 8730,1194 8730,1210C8730,1229 8722,1230 8590,1230C8458,1230 8450,1229 8450,1210Z" style="fill:white;fill-rule:nonzero;"/>
                    </g>
                    <g transform="matrix(1,0,0,1,0,3396.19)">
                        <path d="M8780,1230L8780,870L8830,870L8831,1013L8831,1155L8882,1013C8922,901 8936,870 8951,870C8969,870 8971,875 9041,1070L9075,1165L9078,1018C9080,878 9082,870 9100,870C9119,870 9120,877 9120,1050L9120,1230L9092,1230C9065,1230 9062,1225 9010,1078C8948,902 8960,899 8890,1108L8848,1230L8780,1230Z" style="fill:white;fill-rule:nonzero;"/>
                    </g>
                </g>
            </g>
        </g>
    </g>
    <g transform="matrix(2.5553,0,0,2.5553,14.9298,4.8568)">
        <path d="M29.815,20.446C27.766,17.92 25.254,16.061 21.761,15.81C19.65,15.837 18.432,16.341 16.675,17.212C15.116,17.988 13.951,19.003 12.44,19.867C10.758,20.834 8.518,21.965 6.489,21.883C5.87,21.862 3.562,21.515 2.643,20.827C4.658,26.581 9.962,30.673 16.573,30.673C22.803,30.673 28.215,26.628 30.244,21.134C30.169,20.977 29.815,20.446 29.815,20.446Z" style="fill:white;fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(2.5553,0,0,2.5553,14.9298,4.8568)">
        <path d="M30.978,18.438C31.108,17.648 31.183,16.838 31.183,16.014C31.183,7.966 24.612,1.355 16.565,1.355C8.517,1.355 1.851,7.741 1.824,15.959C1.824,16.708 1.879,17.444 1.988,18.179C1.994,18.24 2.028,18.397 2.035,18.458C4.731,20.793 8.81,19.724 11.295,18.213C13.623,16.797 15.373,15.204 16.878,13.107C18.287,11.139 19.608,9.089 20.309,6.761C24.204,9.695 28.323,14.468 30.978,18.438Z" style="fill:white;fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(2.5553,0,0,2.5553,14.9298,4.8568)">
        <path d="M134.231,23.626L126.204,8.538L123.725,8.538L115.664,23.626L118.81,23.626C119.225,22.836 119.477,22.448 120.314,21.951C122.622,20.576 126.292,18.839 127.293,19.255C128.09,19.915 128.995,21.651 129.942,23.626L134.231,23.626ZM126.244,17.028C124.787,17.335 123.296,17.968 121.676,18.737L121.519,18.526L124.27,13.079L124.549,13.079L126.244,16.749L126.244,17.028Z" style="fill:white;fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(2.5553,0,0,2.5553,14.9298,4.8568)">
        <path d="M80.606,16.082C80.715,12.841 79.046,10.247 75.915,9.103C74.893,8.728 73.64,8.565 72.32,8.531C71.435,8.511 70.529,8.511 69.637,8.531C68.289,8.565 67.016,8.728 65.974,9.103C62.842,10.247 61.167,12.841 61.283,16.082C61.331,17.539 61.657,19.064 62.352,20.14C63.714,22.25 66.308,23.367 69.692,23.585C70.502,23.639 71.326,23.639 72.136,23.591C75.554,23.387 78.168,22.27 79.543,20.14C80.231,19.064 80.558,17.532 80.606,16.082ZM76.486,16.02C76.65,19.343 74.696,21.93 71.625,22.046C71.278,22.06 70.924,22.06 70.583,22.046C67.486,21.971 65.484,19.37 65.647,16.02C65.804,12.691 67.724,10.179 70.59,10.015C70.869,9.995 71.155,10.002 71.441,10.008C74.355,10.117 76.323,12.643 76.486,16.02Z" style="fill:white;fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(2.5553,0,0,2.5553,14.9298,4.8568)">
        <path d="M157.277,8.531L169.036,8.531L168.389,10.322L161.063,10.322L161.063,14.856L167.919,14.856L167.225,16.64L161.063,16.64L161.063,21.842L169.036,21.842L168.389,23.626L157.339,23.626L157.277,8.531Z" style="fill:white;fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(2.5553,0,0,2.5553,14.9298,4.8568)">
        <path d="M37.365,8.531L41.416,8.531L47.66,18.152L53.794,8.531L58.015,8.531L58.015,23.626L53.862,23.626L53.862,13.461L47.898,23.639L46.223,23.639L40.354,13.461L40.354,23.626L37.365,23.626L37.365,8.531Z" style="fill:white;fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(2.5553,0,0,2.5553,14.9298,4.8568)">
        <path d="M83.969,8.531L87.176,8.531C89.94,9.09 93.848,12.909 97.484,17.498L97.681,17.498L97.681,8.531L100.718,8.531L100.718,23.626L97.157,23.626C94.447,19.84 91.254,16.3 87.237,12.984L86.917,12.984L86.917,23.626L83.969,23.626L83.969,8.531Z" style="fill:white;fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(2.5553,0,0,2.5553,14.9298,4.8568)">
        <path d="M136.17,8.531L139.377,8.531C142.141,9.09 146.049,12.909 149.685,17.498L149.882,17.498L149.882,8.531L152.919,8.531L152.919,23.626L149.365,23.626C146.655,19.84 143.462,16.3 139.438,12.984L139.118,12.984L139.118,23.626L136.17,23.626L136.17,8.531Z" style="fill:white;fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(2.5553,0,0,2.5553,14.9298,4.8568)">
        <path d="M103.184,8.531L119.619,8.531L118.966,10.342L112.334,10.342L112.334,23.626L108.236,23.626L108.236,10.342L103.184,10.342L103.184,8.531Z" style="fill:white;fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(2.5553,0,0,2.5553,14.9298,4.8568)">
        <path d="M169.076,2.533C170.301,2.533 171.316,3.493 171.316,4.698C171.316,5.903 170.301,6.863 169.076,6.863C167.85,6.863 166.836,5.903 166.836,4.698C166.836,3.493 167.85,2.533 169.076,2.533ZM169.076,3.077C168.129,3.077 167.387,3.813 167.387,4.698C167.387,5.583 168.136,6.311 169.076,6.311C170.022,6.311 170.764,5.576 170.764,4.698C170.764,3.813 170.022,3.077 169.076,3.077ZM170.172,5.719L169.525,5.719L168.967,4.97L168.722,4.97L168.722,5.719L168.197,5.719L168.197,3.67L169.082,3.67C169.205,3.67 169.307,3.677 169.396,3.69C169.484,3.704 169.566,3.731 169.641,3.779C169.716,3.826 169.777,3.881 169.825,3.956C169.872,4.031 169.893,4.119 169.893,4.228C169.893,4.378 169.859,4.5 169.784,4.596C169.716,4.691 169.614,4.773 169.484,4.834L170.172,5.719ZM169.348,4.303C169.348,4.248 169.334,4.208 169.314,4.174C169.294,4.14 169.26,4.105 169.205,4.085C169.171,4.071 169.13,4.065 169.082,4.058C169.035,4.051 168.98,4.051 168.912,4.051L168.722,4.051L168.722,4.603L168.885,4.603C168.967,4.603 169.042,4.596 169.096,4.589C169.151,4.582 169.198,4.562 169.239,4.534C169.273,4.507 169.3,4.473 169.321,4.439C169.341,4.405 169.348,4.357 169.348,4.303Z" style="fill:white;fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1.125,0,0,1.125,236.343,153)">
        <g transform="matrix(1,0,0,1,-172,-29.3333)">
            <g transform="matrix(2.66667,0,0,2.66667,0,0)">
                <clipPath id="_clip2">
                    <rect x="0" y="0" width="129" height="22"/>
                </clipPath>
                <g clip-path="url(#_clip2)">
                    <path d="M0.894,11.341C0.847,14.309 2.354,17.011 5.347,17.011C7.631,17.011 9.257,15.587 9.679,13.255L10.575,13.255C10.104,16.201 8.219,17.822 5.347,17.822C1.814,17.822 -0.048,14.925 0.001,11.44C-0.046,7.978 1.696,4.616 5.347,4.616C9.139,4.616 10.834,7.733 10.67,11.343L0.894,11.343L0.894,11.341ZM9.775,10.53C9.728,7.879 8.126,5.424 5.347,5.424C2.663,5.424 1.083,8.027 0.894,10.53L9.775,10.53Z" style="fill:white;fill-rule:nonzero;"/>
                    <rect x="12.969" y="0.006" width="0.895" height="17.526" style="fill:white;fill-rule:nonzero;"/>
                    <rect x="16.936" y="0.006" width="0.895" height="17.526" style="fill:white;fill-rule:nonzero;"/>
                    <path d="M20.891,4.915L21.786,4.915L21.786,17.532L20.891,17.532L20.891,4.915ZM20.891,0.006L21.786,0.006L21.786,2.534L20.891,2.534L20.891,0.006Z" style="fill:white;fill-rule:nonzero;"/>
                    <path d="M25.046,13.33C25.188,15.661 26.859,17.011 29.049,17.011C30.627,17.011 32.889,16.52 32.889,14.337C32.889,12.202 30.816,11.882 28.743,11.44C26.648,10.998 24.575,10.433 24.575,7.856C24.575,5.353 26.905,4.616 28.956,4.616C31.525,4.616 33.431,5.769 33.431,8.641L32.536,8.641C32.513,6.357 30.98,5.424 28.956,5.424C27.308,5.424 25.47,6.063 25.47,7.856C25.47,9.868 27.543,10.187 29.498,10.63C31.9,11.095 33.784,11.685 33.784,14.337C33.784,17.062 31.169,17.822 29.049,17.822C26.295,17.822 24.222,16.275 24.15,13.33L25.046,13.33Z" style="fill:white;fill-rule:nonzero;"/>
                    <path d="M35.977,0.006L39.98,0.006L39.98,6.19L40.027,6.19C40.851,5.012 42.004,4.424 43.513,4.424C46.974,4.424 48.67,7.566 48.67,10.879C48.67,14.464 47.045,17.9 43.206,17.9C41.793,17.9 40.616,17.261 39.838,16.059L39.791,16.059L39.791,17.532L35.977,17.532L35.977,0.006ZM39.84,11.025C39.84,13.257 40.454,14.707 42.267,14.707C44.057,14.707 44.668,12.769 44.668,11.025C44.668,9.404 44.057,7.612 42.289,7.612C41.207,7.614 39.84,8.399 39.84,11.025Z" style="fill:white;fill-rule:nonzero;"/>
                    <path d="M50.772,4.79L54.657,4.79L54.657,7.024L54.704,7.024C55.293,5.404 56.659,4.547 58.379,4.547C58.685,4.547 59.014,4.572 59.321,4.644L59.321,8.448C58.803,8.3 58.332,8.203 57.79,8.203C55.813,8.203 54.775,9.627 54.775,11.22L54.775,17.53L50.772,17.53L50.772,4.79Z" style="fill:white;fill-rule:nonzero;"/>
                    <path d="M60.613,4.792L64.617,4.792L64.617,17.532L60.613,17.532L60.613,4.792ZM64.619,3.196L60.615,3.196L60.615,0.006L64.619,0.006L64.619,3.196Z" style="fill:white;fill-rule:nonzero;"/>
                    <path d="M79.08,15.981C79.08,18.067 78.705,21.994 72.979,21.994C70.128,21.994 67.444,21.332 66.973,17.919L70.977,17.919C71.071,18.484 71.284,18.827 71.659,19.023C72.013,19.22 72.508,19.294 73.119,19.294C75.027,19.294 75.214,17.896 75.214,16.326L75.214,15.122L75.168,15.122C74.437,16.3 73.212,16.988 71.917,16.988C68.22,16.988 66.547,14.24 66.547,10.655C66.547,7.268 68.455,4.419 71.917,4.419C73.377,4.419 74.483,5.082 75.19,6.383L75.236,6.383L75.236,4.79L79.076,4.79L79.076,15.981L79.08,15.981ZM72.886,7.613C71.119,7.613 70.555,9.233 70.555,10.803C70.555,12.301 71.215,13.797 72.863,13.797C74.559,13.797 75.29,12.422 75.29,10.826C75.287,9.208 74.674,7.613 72.886,7.613Z" style="fill:white;fill-rule:nonzero;"/>
                    <path d="M81.463,0.006L85.467,0.006L85.467,6.533L85.514,6.533C86.127,5.232 87.775,4.421 89.235,4.421C93.31,4.421 93.638,7.515 93.638,9.356L93.638,17.529L89.635,17.529L89.635,11.37C89.635,9.629 89.824,7.86 87.633,7.86C86.127,7.86 85.467,9.184 85.467,10.585L85.467,17.532L81.463,17.532L81.463,0.006Z" style="fill:white;fill-rule:nonzero;"/>
                    <path d="M95.941,8.72C96.011,7.05 96.765,5.971 97.849,5.309C98.933,4.67 100.346,4.424 101.734,4.424C104.632,4.424 107.434,5.087 107.434,8.694L107.434,14.265C107.434,15.344 107.434,16.523 107.904,17.53L103.876,17.53C103.736,17.139 103.69,16.745 103.641,16.328C102.606,17.458 101.074,17.898 99.615,17.898C97.282,17.898 95.447,16.671 95.447,14.045C95.447,9.896 99.78,10.215 102.559,9.627C103.243,9.479 103.619,9.234 103.619,8.449C103.619,7.492 102.512,7.124 101.688,7.124C100.58,7.124 99.875,7.638 99.686,8.72L95.941,8.72ZM101.122,15.321C103.03,15.321 103.666,14.191 103.572,11.565C103.006,11.934 101.97,12.008 101.1,12.253C100.204,12.473 99.451,12.867 99.451,13.874C99.449,14.904 100.226,15.321 101.122,15.321Z" style="fill:white;fill-rule:nonzero;"/>
                    <path d="M109.877,4.792L113.763,4.792L113.763,6.536L113.809,6.536C114.54,5.26 115.835,4.424 117.32,4.424C118.85,4.424 120.217,4.941 120.877,6.487C121.843,5.137 123.043,4.424 124.692,4.424C128.577,4.424 129.002,7.492 129.002,9.921L129.002,17.53L124.998,17.53L124.998,10.044C124.998,8.671 124.363,7.861 123.325,7.861C121.606,7.861 121.441,9.236 121.441,11.297L121.441,17.53L117.438,17.53L117.438,10.29C117.438,8.791 117.013,7.861 115.931,7.861C114.493,7.861 113.882,8.72 113.882,11.322L113.882,17.532L109.879,17.532L109.879,4.792L109.877,4.792Z" style="fill:white;fill-rule:nonzero;"/>
                </g>
            </g>
        </g>
    </g>
</svg>
