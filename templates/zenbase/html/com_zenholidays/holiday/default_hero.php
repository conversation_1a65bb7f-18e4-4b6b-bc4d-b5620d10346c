<?php
  defined('_JEXEC') or die;
  jimport('joomla.html.html');
  $imgSizeRegex = "/w:.*?q/";
  $rootImgPath = "https://i.assetzen.net/i/". $rootImage->sid ."/w:1920/h:600/q:70.webp";

  // Collect locations
  $item_locale = [];
  foreach ($this->item->locations as $location) {
    array_push($item_locale, $location->name);
  }

  // Get the first 4 images from the trip's image gallery
  $heroImages = array_slice($this->item->images, 0, 4);
?>
<div class="zen-hero">
  <!-- Trip Title and Button -->
  <div class="trip-title-bar">
    <div class="container row">
      <div class="trip-title-header col-12 mx-auto px-0">
        <h1 class="trip-title flex-shrink-1"><?php echo $name; ?></h1>
        <div class="d-flex gap-2 flex-shrink-0">
          <a href="#dates-prices" id="hero-dates-prices-button" class="btn btn-primary d-flex align-items-center justify-content-center gap-2">
            <span class="d-flex align-items-center">Dates & Prices</span>
            <img src="/templates/zenbase/icons/trips/date_range.svg" alt="Date range icon" class="d-flex align-items-center">
          </a>
          <?php
          // Get the video copy items
          $video = ZenModelHelper::getCopyItemsByAlias('com_zenholidays', $id, 'video');
          if ($video && isset($video['video']) && isset($video['video']->items)):
          ?>
          <button class="btn btn-outline-dark" data-bs-toggle="modal" data-bs-target="#videoModal">
            Overview video
            <?php echo file_get_contents(JPATH_BASE . '/templates/zenbase/icons/trips/play_circle.svg'); ?>
          </button>
          <?php endif; ?>
        </div>
      </div>
    </div>
  </div>

  <!-- Trip Details Bar -->
  <div class="trip-details-bar">
    <div class="container row">
      <div class="trip-details-inner col-12 mx-auto px-0">
        <!-- Location -->
        <div class="trip-detail-item">
          <i class="zen-icon zen-icon--orange fontello icon-location"></i>
          <span class="trip-detail-value"><?php echo implode(", ", $item_locale); ?></span>
        </div>

        <!-- Altitude -->
        <div class="trip-detail-item">
          <i class="zen-icon zen-icon--orange fontello icon-ascent"></i>
          <span class="trip-detail-value"><?php echo $altRange['altitude-range']; ?></span>
        </div>

        <!-- Duration -->
        <div class="trip-detail-item">
          <i class="zen-icon zen-icon--orange fontello icon-calendar"></i>
          <span class="trip-detail-value"><?php echo $holidayDuration + 1; ?> days</span>
        </div>

        <!-- Difficulty Level -->
        <div class="trip-detail-item">
          <i class="zen-icon zen-icon--orange fontello icon-grade"></i>
          <div class="difficulty-info-section" onclick="event.preventDefault(); event.stopPropagation();">
            <button class="difficulty-info-btn d-flex align-items-center"
              type="button"
              data-bs-toggle="modal"
              data-bs-target="#global-difficultyModal-<?php echo strtolower(str_replace(' ', '-', $actLevel['activity-level'])); ?>">
              <?php
              // Determine circle color based on difficulty level
              $difficultyColor = '#95C623'; // Default to Easy
              $difficultyLevel = strtolower($actLevel['activity-level']);

              if ($difficultyLevel === 'hardcore') {
                $difficultyColor = '#CD5334';
              } elseif ($difficultyLevel === 'challenging') {
                $difficultyColor = '#FE7720';
              } elseif ($difficultyLevel === 'moderate') {
                $difficultyColor = '#FFBF06';
              }
              ?>
              <span class="difficulty-indicator me-2" style="width: 14px; height: 14px; border-radius: 50%; background-color: <?php echo $difficultyColor; ?>"></span>
              <span class="difficulty-text"><?php echo $actLevel['activity-level']; ?></span>
              <span class="difficulty-icon ms-2">
                <?php echo file_get_contents(JPATH_BASE . '/templates/zenbase/icons/info-rounded.svg'); ?>
              </span>
            </button>
          </div>
        </div>

        <!-- Price -->
        <div class="trip-detail-item price-item">
          <i class="zen-icon zen-icon--orange fontello icon-tag"></i>
          <span class="trip-detail-value price-value">
            <?php echo $fromPrice->currency_symbol.number_format($fromPrice->value); ?>pp
            <span class="price-monthly">or <?php echo $fromPrice->currency_symbol.number_format(($fromPrice->value - 200) / 18); ?>/18 months</span>
          </span>
        </div>
      </div>
    </div>
  </div>

  <!-- Image Grid -->
  <div class="trip-image-bar">
    <div class="container row">
      <div class="hero-image-grid col-12 mx-auto px-0">
        <!-- Main Image -->
        <div class="hero-image main-image" style="background-image: url('https://i.assetzen.net/i/<?php echo $heroImages[0]->sid; ?>/w:1200/h:600/q:70.webp');">
          <button class="gallery-button" data-bs-toggle="modal" data-bs-target="#modal-gallery-<?php echo $this->item->id; ?>">
            <span>View image gallery</span>
            <?php echo file_get_contents(JPATH_BASE . '/templates/zenbase/icons/gallery.svg'); ?>
          </button>
        </div>

        <!-- Side Images -->
        <div class="side-images">
          <div class="hero-image side-image-full<?php
            // Get the video copy items
            $video = ZenModelHelper::getCopyItemsByAlias('com_zenholidays', $id, 'video');
            if ($video && isset($video['video']) && isset($video['video']->items)):
          ?> video-trigger" data-bs-toggle="modal" data-bs-target="#videoModal"<?php endif; ?>" style="background-image: url('https://i.assetzen.net/i/<?php echo $heroImages[1]->sid; ?>/w:840/h:600/q:70.webp');">
            <?php if ($video && isset($video['video']) && isset($video['video']->items)): ?>
              <div class="play-icon d-lg-none">
                <?php echo file_get_contents(JPATH_BASE . '/templates/zenbase/icons/trips/play_circle_white.svg'); ?>
              </div>
            <?php endif; ?>
          </div>
          <div class="hero-image-split">
            <div class="split-image" style="background-image: url('https://i.assetzen.net/i/<?php echo $heroImages[2]->sid; ?>/w:400/h:300/q:70.webp');"></div>
            <div class="split-image" style="background-image: url('https://i.assetzen.net/i/<?php echo $heroImages[3]->sid; ?>/w:400/h:300/q:70.webp');">
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Video Modal -->
<div class="modal fade" id="videoModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content">
      <div class="modal-body">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        <div class="ratio ratio-16x9">
          <?php
          // Load video copy items directly
          $videoData = ZenModelHelper::getCopyItemsByAlias('com_zenholidays', $id, 'video');

          if (isset($videoData['video']) && isset($videoData['video']->items)) {
            foreach ($videoData['video']->items as $item) {
              if (isset($item->content)) {
                // Process the content through Joomla's content plugins
                $item->content = JHtml::_('content.prepare', $item->content);
                echo $item->content;
                break;
              }
            }
          }
          ?>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
:root {
  --zen-utility-bar-height: 40px;
  --zen-header-height: 72px;
}

/* Base styles */
.zen-hero {
  position: relative;
  display: flex;
  flex-direction: column;
}

/* Mobile-first layout fixes - these need to be at the top of mobile styles */
@media (max-width: 992px) {
  /* Fixed header positioning - these must come first */
  .zen-header-container {
    /* position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1030; */
  }


  .zen-body {
    display: flex;
    flex-direction: column;
  }

  .zen-body .hero-image-grid {
    order: -1;
    margin: 0;
    padding: 0;
    gap: 5px;
  }

  /* Rest of mobile styles... */
  .hero-image-grid {
    display: grid;
    grid-template-columns: 69.47% 37.55%;
    gap: 5px;
    margin: 0;
    padding: 0;
    width: 100%;
  }

  .hero-image.main-image {
    aspect-ratio: 876/655 !important; /* Force override */
  }

  .side-images {
    display: flex;
    flex-direction: column;
    gap: 5px;
  }

  .hero-image.side-image-full {
    flex: 2; /* 66.6% */
  }

  .hero-image-split {
    flex: 1; /* 33.3% */
  }

  .play-icon {
    display: block;
  }

  .trip-title-bar {
    padding: 1.5rem 0 0.75rem;
    box-shadow: none;
  }

  .trip-title-header {
    display: flex;
    flex-direction: column;
    align-items: flex-start !important;
    gap: 1.5rem;
    text-align: left !important;
  }

  .trip-title {
    font-size: 2.5rem;
    line-height: 1.2;
    margin: 0;
    text-align: left !important;
  }

  .trip-title-header .btn-primary {
    display: none;
  }

  .trip-details-bar {
    padding: 0 0 1.5rem;
    background: white;
    box-shadow: none;
  }

  .trip-details-inner {
    display: flex;
    flex-direction: row !important;
    flex-wrap: wrap;
    align-items: flex-start !important;
    justify-content: flex-start !important;
    gap: 1rem 2rem;
    text-align: left !important;
  }

  .trip-detail-item {
    width: auto !important;
    justify-content: flex-start !important;
    text-align: left !important;
    margin: 0 !important;
  }

  .trip-detail-value {
    font-size: 1rem;
  }

  .price-monthly {
    font-size: 0.875rem;
  }

  .gallery-button {
    bottom: 10px;
    right: 10px;
    padding: 4px 10px;
  }

  .video-trigger {
    cursor: pointer;
    position: relative;
  }

  .play-icon {
    display: block;
  }

  .side-image-full.video-trigger {
    position: relative;
  }

  .side-image-full.video-trigger::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(0deg, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0) 100%);
    pointer-events: none;
  }
}

/* Rest of existing styles */
.hero-image-grid {
  display: grid;
  grid-template-columns: 69.47% 29.55%;
  gap: 5px;
  /* max-width: 1262px; */
  margin: 0 auto;
  padding: 0 15px;
  justify-content: center;
  width: 100%;
}

.hero-image {
  position: relative;
  overflow: hidden;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  width: 100%;
}

.hero-image.main-image {
  aspect-ratio: 876/405;
}

.side-images {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.hero-image.side-image-full {
  flex: 2; /* Takes up 66.6% of the space */
}

.hero-image-split {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 5px;
  flex: 1; /* Takes up 33.3% of the space */
}

.split-image {
  position: relative;
  overflow: hidden;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
}

.video-trigger {
  cursor: pointer;
  position: relative;
}

.play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
  pointer-events: none; /* Allow clicks to pass through to the video trigger */
}

.play-icon svg {
  width: 53px;
  height: 53px;
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
}

.hero-image-container {
  position: relative;
  overflow: hidden;
}

.object-fit-cover {
  object-fit: cover;
}

.gx-2 {
  --bs-gutter-x: 5px;
}

.gy-1 {
  --bs-gutter-y: 5px;
}

.trip-details-bar {
  background: white;
  padding: 1rem 0;
  position: relative;
  z-index: 10;
  box-shadow: none;
}

.trip-details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.trip-title {
  margin: 0;
}

.trip-details-inner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  flex-wrap: wrap;
}

.trip-detail-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
}

.trip-detail-item i {
  font-size: 20px;
  color: #FE7720;
}

.trip-detail-value {
  font-size: 14px;
  font-weight: 500;
  color: #000;
}

.price-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.price-value {
  color: #FE7720;
  font-weight: 600;
}

.price-monthly {
  font-size: 12px;
  color: #666;
  margin-left: 0.25rem;
}

.button-item .btn {
  padding: 0.5rem 1rem;
  font-size: 14px;
}

@media (max-width: 1200px) {
  .trip-details-inner {
    justify-content: space-between;
  }

  .trip-detail-item {
    margin: 0.25rem;
  }
}

@media (max-width: 768px) {
  .trip-details-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .trip-details-inner {
    flex-direction: column;
    align-items: center;
  }

  .trip-detail-item {
    width: 100%;
    justify-content: center;
  }
}

.gallery-button {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: white;
  color: #000;
  border: none;
  border-radius: 9999px;
  padding: 6px 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.gallery-button:hover {
  background: #f5f5f5;
}

.gallery-button i {
  font-size: 18px;
}

.gallery-button span {
  font-size: 14px;
  font-weight: 500;
}

.trip-title-bar {
  background: white;
  padding: 1rem 0;
  position: relative;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.trip-title-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.trip-title-bar .btn {
  padding: 6px 1rem;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.trip-title-bar .btn svg {
  width: 18px;
  height: 18px;
}

.trip-title-bar .btn-outline-dark {
  font-family: 'fustatregular', sans-serif;
  border-color: #000;
  color: #000;
  background: transparent;
  border-width: 1px;
}

.trip-title-bar .btn-outline-dark:hover {
  background: #000;
  color: #fff;
}

.trip-title-bar .btn-outline-dark:hover svg {
  fill: #fff;
}

.difficulty-info-btn {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0;
  font-size: 14px;
  font-weight: 500;
  color: #000;
}

.difficulty-info-btn:hover {
  opacity: 0.8;
}

#videoModal .modal-dialog {
  max-width: 70%;
  width: 70%;
}

#videoModal .modal-body {
  padding: 0;
  background-color: transparent;
  position: relative;
}

#videoModal .btn-close {
  position: absolute;
  right: 15px;
  top: 15px;
  color: white;
  font-size: 1.25rem;
  opacity: 1;
  z-index: 3;
  background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e") center/1em auto no-repeat;
}

#videoModal .modal-content {
  background-color: transparent!important;
  border: none;
  font-size: 14.5px;
  line-height: normal;
  font-weight: bold;
}

#videoModal .ratio {
  background-color: black;
  border-radius: 8px;
  overflow: hidden;
}

#videoModal .ratio iframe {
  z-index: 2;
}

@media (max-width: 767px) {
  #videoModal .modal-dialog {
    width: 95%;
    max-width: 95%;
    margin: 0 auto;
  }
}

.video-container {
  max-height: 80vh;
  overflow-y: auto;
  padding-right: 10px;
}

.video-container::-webkit-scrollbar {
  width: 8px;
}

.video-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.video-container::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.video-container::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.video-item {
  background: #000;
  border-radius: 8px;
  overflow: hidden;
}

.video-item h4 {
  color: white;
  padding: 10px;
  margin: 0;
}


</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const videoModal = document.getElementById('videoModal');
  if (videoModal) {
    // Function to detect mobile devices
    function isMobileDevice() {
      return window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    videoModal.addEventListener('show.bs.modal', function (event) {
      const iframe = this.querySelector('iframe');
      if (iframe) {
        let currentSrc = iframe.src;

        // Add autoplay parameter for mobile devices only
        if (isMobileDevice()) {
          // Check if URL already has parameters
          const separator = currentSrc.includes('?') ? '&' : '?';
          // Add autoplay=1 and mute=1 if not already present
          if (!currentSrc.includes('autoplay=')) {
            currentSrc += separator + 'autoplay=1&mute=1';
          }
        } else {
          // Remove autoplay and mute parameters for desktop
          currentSrc = currentSrc.replace(/[?&](autoplay=1|mute=1)/g, '');
          // Clean up any double ampersands or trailing question marks
          currentSrc = currentSrc.replace(/[?&]&/g, '&').replace(/[?&]$/, '');
        }

        // Only reload if the URL has changed
        if (currentSrc !== iframe.src) {
          iframe.src = currentSrc;
        }
      }
    });

    videoModal.addEventListener('hide.bs.modal', function (event) {
      const iframe = this.querySelector('iframe');
      if (iframe) {
        // Pause the video when modal is closed
        iframe.src = iframe.src;
      }
    });
  }

  // Add click outside handler for gallery modal
  const galleryModal = document.getElementById('modal-gallery-<?php echo $this->item->id; ?>');
  if (galleryModal) {
    galleryModal.addEventListener('click', function(event) {
      if (event.target === galleryModal) {
        const modal = bootstrap.Modal.getInstance(galleryModal);
        if (modal) {
          modal.hide();
        }
      }
    });
  }
});
</script>

<script>
  // The deep-link-button-handlers.js script will handle the dates-prices button automatically
  // The updated script ensures buttons work consistently even when clicked multiple times
  // No need for custom event handlers here
</script>