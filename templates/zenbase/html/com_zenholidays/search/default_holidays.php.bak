<?php
defined('_JEXEC') or die;

use <PERSON><PERSON><PERSON>\CMS\Language\Text;
use Jo<PERSON><PERSON>\CMS\Factory;
use <PERSON><PERSON>la\CMS\Uri\Uri;
use <PERSON><PERSON><PERSON>\CMS\HTML\HTMLHelper;
use <PERSON><PERSON><PERSON>\CMS\Uri\JUri;

jimport('mrzen.helpers.ZenAngularHelper');

// Add required scripts
HTMLHelper::_('jquery.framework');
ZenAngularHelper::addScript(Uri::base(true).'/libraries/mzelastic/js/filtersController.js');

// Add the equalizer script
Factory::getDocument()->addScript(Uri::base(true) . '/templates/zenbase/js/search_equalizer.js');

// Add window functions for difficulty handling
$doc = Factory::getDocument();
$script = <<<JS
window.getDifficultyGradeImage = function(level) {
    if (!level) return 'grade-1.svg';
    switch(level.toLowerCase()) {
        case 'gentle': return 'grade-1.svg';
        case 'easy': return 'grade-1.svg';
        case 'moderate': return 'grade-2.svg';
        case 'challenging': return 'grade-3.svg';
        case 'hardcore': return 'grade-4.svg';
        default: return 'grade-1.svg';
    }
};

window.getDifficultyDescription = function(level) {
    if (!level) return 'Information about this difficulty level is not available.';
    switch(level.toLowerCase()) {
        case 'easy': return 'Perfect for beginners or those looking for a gentle adventure. These trips involve light activities with moderate distances and minimal elevation gain.';
        case 'moderate': return 'Suitable for those with some hiking experience. These trips include longer distances and moderate elevation gain, requiring a good level of fitness.';
        case 'challenging': return 'For experienced hikers seeking a more demanding adventure. These trips involve significant distances and elevation gain, requiring excellent fitness and endurance.';
        case 'hardcore': return 'Our most demanding trips for seasoned adventurers. These expeditions involve challenging terrain, significant elevation gain, and require exceptional fitness and experience.';
        default: return 'Information about this difficulty level is not available.';
    }
};

// Add the getDifficultyColor function to the window object so it's globally available
window.getDifficultyColor = function(level) {
    if (!level) return '#95C623';
    switch(level.toLowerCase()) {
        case 'gentle': return '#95C623';
        case 'easy': return '#95C623';
        case 'moderate': return '#FFBF06';
        case 'challenging': return '#FE7720';
        case 'hardcore': return '#CD5334';
        default: return '#95C623';
    }
};

// Handle refine button click for dynamically added elements
jQuery(document).on('click', '.js-zen-mobile-nav-toggle', function(e) {
    e.preventDefault();
    e.stopPropagation();
    var _this = jQuery(this);
    _this.toggleClass("active");
    jQuery("body").toggleClass(_this.data("menu-type"));
    jQuery(_this.data("selector")).toggleClass("active");
});
JS;
$doc->addScriptDeclaration($script);
?>
<div id="results"
     class="alt results"
     ng-show="hitCount > 0"
     ng-class="{'placeholder-glow': isLoading}"
     ng-cloak>

  <!-- Results header bar -->
  <div class="zen-results-header bg-black text-white py-3 px-4 mb-4 rounded-3 d-flex justify-content-between align-items-center">
    <div class="zen-results-count d-flex align-items-center">
      <span class="zen-search__hit-loading" id="hits-loading" data-ng-show="isLoading" class="d-inline-block">
        <div class="spinner-border zen-text zen-text--primary" role="status">
          <span class="visually-hidden"><?php echo Text::_('ZEN_SEARCH_RESULTS_LOADING'); ?></span>
        </div>
      </span>
      <span data-ng-show="!isLoading">
        <span class="text-primary fw-bold fg-orange">{{hitCount}}</span> <span class="fg-white"><?php echo Text::_('ZEN_SEARCH_TREKS_FOUND'); ?></span>
      </span>
      <a class="zen-link zen-link--primary ms-3" role="button" data-ng-click="resetFilters()">
        <!-- <?php echo Text::_('ZEN_SEARCH_RESET_BTN'); ?> -->
      </a>
    </div>
    <button class="js-zen-mobile-nav-toggle btn btn-outline-1 bd-white py-2 px-3 rounded btn-link text-white d-flex d-md-none align-items-center gap-2 p-0" data-selector=".zen-facet__main-filters" data-menu-type="zen-filters-open" id="refine">
      <!-- <span class="zen-refine-btn-close"> -->
        Sort & Filter
      <!-- </span> -->
    </button>
    <div class="zen-results-sort d-none d-md-flex align-items-center gap-3">
      <div class="dropdown-like-select">
        <select class="btn btn-outline-1 bd-white py-2 px-3 rounded btn-link text-white d-flex align-items-center gap-2" id="search-order-by" data-ng-model="search_filters.sort">
          <option value="ordering|ASC" selected>Best Sellers</option>
          <option value="_score|DESC" data-ng-show="search_filters.text !== ''">Relevancy</option>
          <option value="from_prices.{{esConfig.currency}}|ASC">Price (Ascending)</option>
          <option value="from_prices.{{esConfig.currency}}|DESC">Price (Descending)</option>
          <option value="duration|ASC">Duration</option>
        </select>
      </div>
    </div>
  </div>

  <div data-ng-if="$index % 10 === 0" id="page-{{$index/10}}" class="search-anchor d-none"><?php echo Text::_('ZEN_SEARCH_ITEM_PAGE'); ?> {{$index/10}}</div>
  <div class="row g-4">
    <!-- First two holiday cards -->
    <div data-ng-repeat="result in holidays | limitTo:2 track by $index" class="col-12 col-md-6 col-lg-4">
      <div class="zen-card zen-card--related h-100" ng-class="{'d-none': isLoading}" data-href="/holidays/{{result._source.alias}}">
        <a class="zen-link zen-link--dark zen-link--image-overlay-expand d-inline-block" data-ng-href="/holidays/{{result._source.alias}}">
          <div class="zen-card__body">
            <div class="zen-card__image zen-card__image--max-height mb-3">
              <img class="img-fluid" data-ng-src="{{ result._source.list_image }}" alt="{{ result._source.name }}" ng-if="result._source.list_image">
              <img class="img-fluid" data-ng-src="https://via.placeholder.com/420x300&text=..." alt="{{ result._source.name }}" ng-if="!result._source.list_image">
              <div class="zen-card__image-overlay">
                <ul class="zen-list zen-list--inline date-list d-flex align-items-center justify-content-center">
                  <li class="zen-text zen-text--light overlay-message text-center">
                    <?php echo Text::_('ZEN_SEARCH_RESULTS_AVAILABLE_DATES'); ?>
                  </li>
                  <li class="list-inline-item"
                      data-ng-repeat="month in result.operating_months | orderBy:'operates'"
                      data-ng-if="month.operates">
                    <span class="zen-text zen-text--light">
                      {{ month.month | month_name }}
                    </span>
                  </li>
                </ul>
              </div>
            </div>

            <div class="zen-card__info-wrap">
              <h5 class="zen-title zen-title--font-semibold zen-capitalize">
                {{ result._source.name }}
              </h5>

              <p class="zen-text zen-text--subtitle mb-1">
                <span>{{ result._source.location_text | join_unique: ', ' }}</span>
                <span data-ng-if="result._source.duration[0] - 1 > 0">
                  {{ result._source.duration[0] - 1 }}
                  <span data-ng-show="result._source.duration[0] == 1"><?php echo Text::_('ZEN_HOLIDAY_SINGLE_NIGHT'); ?></span>
                  <span data-ng-show="result._source.duration[0] > 1"><?php echo Text::_('ZEN_HOLIDAY_NIGHTS'); ?></span>
                </span>
              </p>

              <div class="zen-card__info">
                <div class="row">
                  <div class="col">
                    <div class="d-flex justify-content-start align-items-center h-100">
                      <img src="/templates/zenbase/icons/altitude.svg" alt="">
                      <span>{{ result._source.categories['Altitude Range'][0] }}</span>
                    </div>
                  </div>
                  <div class="col text-right">
                    <div class="d-flex justify-content-end align-items-center h-100">
                      <div class="difficulty-info-section" onclick="event.preventDefault(); event.stopPropagation();">
                        <button class="difficulty-info-btn d-flex align-items-center"
                          type="button"
                          data-bs-toggle="modal"
                          data-bs-target="#global-difficultyModal-{{result._source.categories['Activity Level'][0].toLowerCase()}}">
                          <span class="difficulty-icon">
                            <?php echo file_get_contents(JPATH_BASE . '/templates/zenbase/icons/info-rounded.svg'); ?>
                          </span>
                          <span class="difficulty-text">{{ result._source.categories['Activity Level'][0] }}</span>
                          <span class="difficulty-indicator ms-2"
                                style="width: 14px; height: 14px; border-radius: 50%;"
                                data-ng-class="{
                                  'bg-easy': result._source.categories['Activity Level'][0].toLowerCase() === 'easy' || result._source.categories['Activity Level'][0].toLowerCase() === 'gentle',
                                  'bg-moderate': result._source.categories['Activity Level'][0].toLowerCase() === 'moderate',
                                  'bg-challenging': result._source.categories['Activity Level'][0].toLowerCase() === 'challenging',
                                  'bg-hardcore': result._source.categories['Activity Level'][0].toLowerCase() === 'hardcore'
                                }"></span>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Preview Bullets -->
              <div class="zen-text zen-text--text-df mb-3 preview-bullets-container"
                   data-ng-attr-data-preview-bullets="{{result._source.id}}"
                   data-ng-attr-id="preview-bullets-{{result._source.id}}">
                <!-- Preview bullets will be loaded here via JavaScript -->
              </div>

              <div class="zen-flex-end zen-price">
                <div class="row w-100 m-0">
                  <div class="col-12 d-flex justify-content-between align-items-center p-0">
                    <span>
                      <?php echo Text::_('ZEN_CARD_FROM'); ?>
                      <span>{{ result._source.from_prices[esConfig.currency] | price }}<?php echo Text::_('ZEN_HOLIDAY_PRICING_PP'); ?></span>
                    </span>
                    <span class="zen-text zen-text--default-light zen-uppercase mx-2">
                      <?php echo Text::_('ZEN_SITE_OR'); ?>
                    </span>
                    <span>
                      <span class="p-0">
                        £{{ (result._source.from_prices[esConfig.currency] / 18).toFixed() }} / 18
                        <?php echo Text::_('ZEN_HOLIDAY_MONTHS'); ?>
                      </span>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a>
      </div>
    </div>

    <!-- Message card at position 3 -->
    <div class="col-12 col-md-6 col-lg-4">
      <div class="zen-card zen-card--related h-100 zen-card--message">
        <div class="zen-card__body">
          <div class="zen-card__info-wrap text-left">
            <h3 class="zen-title zen-title--font-semibold mb-4"><?php echo Text::_('TPL_ZENBASE_MESSAGE_CARD_TITLE'); ?></h3>
            <ul class="zen-list zen-list--unstyled mb-4">
              <li class="d-flex align-items-center mb-3">
                <img src="/templates/zenbase/icons/choose-rosette.svg" alt="" class="me-2" width="24" height="24">
                <span><?php echo Text::_('TPL_ZENBASE_MESSAGE_CARD_POINT_1'); ?></span>
              </li>
              <li class="d-flex align-items-center mb-3">
                <img src="/templates/zenbase/icons/choose-clipboard.svg" alt="" class="me-2" width="24" height="24">
                <span><?php echo Text::_('TPL_ZENBASE_MESSAGE_CARD_POINT_2'); ?></span>
              </li>
              <li class="d-flex align-items-center mb-3">
                <img src="/templates/zenbase/icons/choose-seedling.svg" alt="" class="me-2" width="24" height="24">
                <span><?php echo Text::_('TPL_ZENBASE_MESSAGE_CARD_POINT_3'); ?></span>
              </li>
              <li class="d-flex align-items-center mb-3">
                <img src="/templates/zenbase/icons/choose-handshake.svg" alt="" class="me-2" width="24" height="24">
                <span><?php echo Text::_('TPL_ZENBASE_MESSAGE_CARD_POINT_4'); ?></span>
              </li>
            </ul>
            <p class="zen-text mb-2"><?php echo Text::_('TPL_ZENBASE_MESSAGE_CARD_TEXT'); ?></p>

          </div>
        </div>
      </div>
    </div>

    <!-- Remaining holiday cards -->
    <div data-ng-repeat="result in holidays | limitTo:((hitCount || 0) - 2):2 track by $index" class="col-12 col-md-6 col-lg-4">
      <div class="zen-card zen-card--related h-100" ng-class="{'d-none': isLoading}" data-href="/holidays/{{result._source.alias}}">
        <a class="zen-link zen-link--dark zen-link--image-overlay-expand d-inline-block" data-ng-href="/holidays/{{result._source.alias}}">
          <div class="zen-card__body">
            <div class="zen-card__image zen-card__image--max-height mb-3">
              <img class="img-fluid" data-ng-src="{{ result._source.list_image }}" alt="{{ result._source.name }}" ng-if="result._source.list_image">
              <img class="img-fluid" data-ng-src="https://via.placeholder.com/420x300&text=..." alt="{{ result._source.name }}" ng-if="!result._source.list_image">
              <div class="zen-card__image-overlay">
                <ul class="zen-list zen-list--inline date-list d-flex align-items-center justify-content-center">
                  <li class="zen-text zen-text--light overlay-message text-center">
                    <?php echo Text::_('ZEN_SEARCH_RESULTS_AVAILABLE_DATES'); ?>
                  </li>
                  <li class="list-inline-item"
                      data-ng-repeat="month in result.operating_months | orderBy:'operates'"
                      data-ng-if="month.operates">
                    <span class="zen-text zen-text--light">
                      {{ month.month | month_name }}
                    </span>
                  </li>
                </ul>
              </div>
            </div>

            <div class="zen-card__info-wrap">
              <h5 class="zen-title zen-title--font-semibold zen-capitalize">
                {{ result._source.name }}
              </h5>

              <p class="zen-text zen-text--subtitle mb-1">
                <span>{{ result._source.location_text | join_unique: ', ' }}</span>
                <span data-ng-if="result._source.duration[0] - 1 > 0">
                  {{ result._source.duration[0] - 1 }}
                  <span data-ng-show="result._source.duration[0] == 1"><?php echo Text::_('ZEN_HOLIDAY_SINGLE_NIGHT'); ?></span>
                  <span data-ng-show="result._source.duration[0] > 1"><?php echo Text::_('ZEN_HOLIDAY_NIGHTS'); ?></span>
                </span>
              </p>

              <div class="zen-card__info">
                <div class="row">
                  <div class="col">
                    <div class="d-flex justify-content-start align-items-center h-100">
                      <img src="/templates/zenbase/icons/altitude.svg" alt="">
                      <span>{{ result._source.categories['Altitude Range'][0] }}</span>
                    </div>
                  </div>
                  <div class="col text-right">
                    <div class="d-flex justify-content-end align-items-center h-100">
                      <div class="difficulty-info-section" onclick="event.preventDefault(); event.stopPropagation();">
                        <button class="difficulty-info-btn d-flex align-items-center"
                          type="button"
                          data-bs-toggle="modal"
                          data-bs-target="#global-difficultyModal-{{result._source.categories['Activity Level'][0].toLowerCase()}}">
                          <span class="difficulty-icon">
                            <?php echo file_get_contents(JPATH_BASE . '/templates/zenbase/icons/info-rounded.svg'); ?>
                          </span>
                          <span class="difficulty-text">{{ result._source.categories['Activity Level'][0] }}</span>
                          <span class="difficulty-indicator ms-2"
                                style="width: 14px; height: 14px; border-radius: 50%;"
                                data-ng-class="{
                                  'bg-easy': result._source.categories['Activity Level'][0].toLowerCase() === 'easy' || result._source.categories['Activity Level'][0].toLowerCase() === 'gentle',
                                  'bg-moderate': result._source.categories['Activity Level'][0].toLowerCase() === 'moderate',
                                  'bg-challenging': result._source.categories['Activity Level'][0].toLowerCase() === 'challenging',
                                  'bg-hardcore': result._source.categories['Activity Level'][0].toLowerCase() === 'hardcore'
                                }"></span>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Preview Bullets -->
              <div class="zen-text zen-text--text-df mb-3 preview-bullets-container"
                   data-ng-attr-data-preview-bullets="{{result._source.id}}"
                   data-ng-attr-id="preview-bullets-{{result._source.id}}">
                <!-- Preview bullets will be loaded here via JavaScript -->
              </div>

              <div class="zen-flex-end zen-price">
                <div class="row w-100 m-0">
                  <div class="col-12 d-flex justify-content-between align-items-center p-0">
                    <span>
                      <?php echo Text::_('ZEN_CARD_FROM'); ?>
                      <span>{{ result._source.from_prices[esConfig.currency] | price }}<?php echo Text::_('ZEN_HOLIDAY_PRICING_PP'); ?></span>
                    </span>
                    <span class="zen-text zen-text--default-light zen-uppercase mx-2">
                      <?php echo Text::_('ZEN_SITE_OR'); ?>
                    </span>
                    <span>
                      <span class="p-0">
                        £{{ (result._source.from_prices[esConfig.currency] / 18).toFixed() }} / 18
                        <?php echo Text::_('ZEN_HOLIDAY_MONTHS'); ?>
                      </span>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a>
      </div>
    </div>
  </div>
  <div>
    <?php require(JPATH_BASE . '/templates/zenbase/html/com_zenholidays/search/default_pagination.php'); ?>
  </div>
</div>
<div ng-show="hitCount == 0">
  <?php
    $noresults = ZenModelHelper::getCopyItemsByReference('no-results');
    echo $noresults[0]->content;
  ?>
  <p>
    <a data-ng-click="loadLink('/holidays#');">
      <?php echo Text::_('ZEN_SEARCH_SHOW_ALL_TRIPS'); ?>
    </a>
  </p>
</div>

<style>
  /* Difficulty level colors */
  .bg-easy {
    background-color: #95C623 !important;
  }
  .bg-moderate {
    background-color: #FFBF06 !important;
  }
  .bg-challenging {
    background-color: #FE7720 !important;
  }
  .bg-hardcore {
    background-color: #CD5334 !important;
  }

  /* Custom styling for the select dropdown to make it look like the previous Bootstrap dropdown */
  .dropdown-like-select {
    position: relative;
    display: inline-block;
  }

  .dropdown-like-select select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-color: transparent;
    border: 1px solid rgba(255, 255, 255, 0.3);
    cursor: pointer;
    padding-right: 30px !important;
    font-weight: normal;
    min-width: 180px;
    text-align: left;
  }

  .dropdown-like-select::after {
    content: '';
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid white;
    pointer-events: none;
  }

  .dropdown-like-select select:focus {
    outline: none;
    box-shadow: none;
    border-color: rgba(255, 255, 255, 0.5);
  }

  .dropdown-like-select select option {
    background-color: #343a40;
    color: white;
    padding: 8px 12px;
  }

  /* Fix for Firefox which doesn't respect the styling of options */
  @-moz-document url-prefix() {
    .dropdown-like-select select {
      background-color: rgba(0, 0, 0, 0.2);
    }
  }

  /* Fix for IE */
  .dropdown-like-select select::-ms-expand {
    display: none;
  }
</style>