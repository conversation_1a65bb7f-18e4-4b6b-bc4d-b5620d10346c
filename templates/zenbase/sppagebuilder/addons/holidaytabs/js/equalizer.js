/*==============================================================================
// File:        equalizer.js
// Package:     Joomla / Mr Zen
// Synopsis:    Equalize Card Heights
//============================================================================*/

// Debounce helper
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function equalizeCardHeights() {
    // Cache DOM queries
    const grid = document.querySelector('.holiday-grid');
    if (!grid) return;

    const cards = Array.from(grid.querySelectorAll('.zen-card'));
    if (!cards.length) return;

    // Early return for mobile with simpler logic
    if (window.innerWidth <= 576) {
        // Use a single class to reset heights via CSS
        grid.classList.add('mobile-layout');
        return;
    }

    // Use CSS Grid for equal heights
    grid.classList.add('equalize-grid');
}

// Debounce the function to prevent multiple calls
const debouncedEqualize = debounce(equalizeCardHeights, 150);

// Use ResizeObserver instead of window resize event
const resizeObserver = new ResizeObserver(debouncedEqualize);
resizeObserver.observe(document.body);

// Initialize on DOMContentLoaded
document.addEventListener('DOMContentLoaded', equalizeCardHeights);

// Handle tab changes more efficiently
document.addEventListener('click', (e) => {
    if (e.target.closest('.sppb-tab')) {
        // Use requestAnimationFrame to ensure DOM is updated
        requestAnimationFrame(equalizeCardHeights);
    }
}); 