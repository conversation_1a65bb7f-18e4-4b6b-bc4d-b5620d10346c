# SP Page Builder Holiday Tabs Addon

This addon is a custom component for SP Page Builder that displays holidays grouped by categories in a tabbed interface.

## File Structure

```
holidaytabs/
├── admin.php           # Admin configuration for the addon
├── site.php           # Frontend rendering logic
├── ajax.php           # AJAX endpoint for lazy loading
├── css/
│   └── styles.css     # Addon-specific styles
└── README.md          # This documentation
```

## Element Settings

The addon is configured through the SP Page Builder interface with the following settings:

### Categories
Categories are configured through a repeatable interface where each item can be configured with:
- **Category**: Select a holiday category to display as a tab
- **Custom Label**: Override the default category name in the tab
- **Icon**: Upload an SVG icon to display next to the tab label

The order of categories in the interface determines their display order in the tabs.

### Display Settings
- Title: Optional heading text for the addon
- Heading Tag: HTML tag for the title (h1-h6)
- Custom Class: Additional CSS classes for styling

### Style Options
- Tab Style: Choose between Default, Pills, or Underline styles
- Tab Background Color: Customize the background of tabs
- Tab Text Color: Set the color of tab text
- Active Tab Background Color: Style the currently active tab
- Active Tab Text Color: Set the text color for the active tab

### Category Display
- Each category tab shows:
  - Category name (or custom label if set)
  - Optional SVG icon
  - Category description at the top of the content
  - Up to 4 featured holidays from that category
  - "View more" link to the full category listing

## How It Works

### Integration with SP Page Builder

The addon integrates with SP Page Builder through two main files:

1. `admin.php`: Defines the addon's configuration options available in the SP Page Builder admin interface
2. `site.php`: Contains the frontend rendering logic and database interactions

### Request Lifecycle

1. **Initial Page Load**
   - SP Page Builder loads the addon during page rendering
   - Only the first tab's content is loaded initially
   - Empty placeholder divs are created for other tabs
   - JavaScript handlers are initialized for tab switching

2. **Tab Switching**
   - When a user clicks a tab, the JavaScript handler checks if content is loaded
   - For unloaded tabs, an AJAX request is made to fetch content
   - A loading spinner is displayed during content fetch
   - Content is loaded via AJAX and cached in the DOM

3. **AJAX Content Loading**
   - `ajax.php` handles the AJAX requests for tab content
   - The `getTabContent` method renders individual tab content
   - Content is returned and injected into the appropriate tab pane
   - Loaded content is marked with `data-loaded="true"` to prevent reloading

4. **Performance Benefits**
   - Reduced initial page load time
   - Fewer initial database queries
   - Content loaded only when needed
   - Content cached in DOM after first load

### Data Retrieval

For each tab:
- Category information is fetched from `#__categories`
- Holidays are retrieved using a complex JOIN query across multiple tables
- Holiday versions and their details are loaded using the `HolidayModel` trait

### Key Features

1. **Lazy Loading**
   - Initial load only includes first tab
   - Other tabs load via AJAX on demand
   - Loading states with spinners
   - Error handling for failed requests

2. **Category-based Tabs**
   - Dynamic tab creation based on selected categories
   - Smooth tab transitions with fade effects
   - Mobile-responsive tab navigation

3. **Holiday Cards**
   - Featured holiday highlighting
   - Location and duration display
   - Altitude and activity level indicators
   - Price display with monthly payment options
   - Responsive grid layout

### CSS Structure

The addon's styles (`styles.css`) are organized into sections:

- Tab navigation and content
- Category headers
- Holiday grid layout
- Card components
- Loading states and transitions
- Responsive breakpoints

### Dependencies

The addon relies on several Joomla extensions and libraries:

- SP Page Builder core
- MrZen Holiday Model trait
- Joomla Factory and Text classes
- Custom Zen template helpers
- jQuery for AJAX handling

## Usage

1. Install the addon in SP Page Builder
2. Add the Holiday Tabs element to a page
3. Select categories in the element settings
4. Configure any additional display options
5. Save and publish the page

## Development Notes

- The addon uses the HolidayModel trait for consistent holiday data handling
- Featured status is managed through the `featured` column in `#__zenholidays`
- Monthly payment calculations consider booking date and balance due date
- Debug information can be enabled for development purposes
- AJAX requests use Joomla's standard routing with com_sppagebuilder 