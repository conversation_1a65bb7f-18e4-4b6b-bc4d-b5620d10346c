<?php
defined('_JEXEC') or die('Restricted access');

use Joomla\CMS\Factory;

/**
 * AJAX handler for the holidaytabs addon
 */
class SppagebuilderAddonHolidaytabsAjax {
    
    /**
     * Get tab content via AJAX
     */
    public static function getTabContent() {
        $input = Factory::getApplication()->input;
        $category_id = $input->getInt('category_id', 0);
        
        if (!$category_id) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid category ID']);
            die();
        }
        
        try {
            // Include the site.php file to access the addon class
            require_once __DIR__ . '/site.php';
            
            // Get the addon instance
            $addon = new SppagebuilderAddonHolidaytabs((object)['settings' => []]);
            
            // Get the tab content using the public method
            $content = $addon->getTabContent($category_id);
            
            echo $content;
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Error loading content: ' . $e->getMessage()]);
        }
        die();
    }
} 