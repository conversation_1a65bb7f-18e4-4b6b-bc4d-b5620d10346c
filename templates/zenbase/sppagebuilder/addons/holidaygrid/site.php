<?php
/**
 * @package SP Page Builder
 * <AUTHOR> http://www.joomshaper.com
 * @copyright Copyright (c) 2010 - 2016 JoomShaper
 * @license http://www.gnu.org/licenses/gpl-2.0.html GNU/GPLv2 or later
*/
//no direct accees
defined('_JEXEC') or die('restricted aceess');

use <PERSON><PERSON><PERSON>\CMS\Factory;
use Jo<PERSON><PERSON>\CMS\Language\Text;
use <PERSON><PERSON><PERSON>\CMS\Router\Route;
use <PERSON><PERSON><PERSON>\CMS\Uri\Uri;
use <PERSON><PERSON><PERSON>\CMS\HTML\HTMLHelper;

class SppagebuilderAddonHolidaygrid extends SppagebuilderAddons
{
    public function render()
    {
        $class = (isset($this->addon->settings->class) && $this->addon->settings->class) ? $this->addon->settings->class : '';
        $title = (isset($this->addon->settings->title) && $this->addon->settings->title) ? $this->addon->settings->title : '';
        $heading_selector = (isset($this->addon->settings->heading_selector) && $this->addon->settings->heading_selector) ? $this->addon->settings->heading_selector : 'h3';
        $filter_type = (isset($this->addon->settings->filter_type) && $this->addon->settings->filter_type) ? $this->addon->settings->filter_type : 'continent';
        $filter_value = (isset($this->addon->settings->filter_value) && $this->addon->settings->filter_value) ? $this->addon->settings->filter_value : '';
        $limit = (isset($this->addon->settings->limit) && $this->addon->settings->limit) ? (int)$this->addon->settings->limit : 12;

        // Load CSS
        $doc = Factory::getDocument();
        $doc->addStyleSheet(Uri::root(true) . '/templates/zenbase/sppagebuilder/addons/holidaygrid/css/styles.css');

        // Add JavaScript to document head
        /*
        $doc->addScriptDeclaration('
            function toggleHolidayGridDebug(button) {
                var content = button.nextElementSibling;
                if (content.style.display === "none") {
                    content.style.display = "block";
                    button.textContent = "Hide Debug Info";
                } else {
                    content.style.display = "none";
                    button.textContent = "Show Debug Info";
                }
            }
        ');
        */

        // Debug output
        /*
        $debug = '<div class="sppb-addon-holidaygrid-debug" style="margin-bottom: 20px;">';
        $debug .= '<button type="button" class="btn btn-info" onclick="toggleHolidayGridDebug(this)">Show Debug Info</button>';
        $debug .= '<div class="debug-content" style="display: none; margin-top: 10px; padding: 10px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px;">';
        $debug .= '<strong>Debug Information:</strong><br>';
        $debug .= 'Filter Type: ' . htmlspecialchars($filter_type) . '<br>';
        $debug .= 'Filter Value: ' . htmlspecialchars($filter_value) . '<br>';
        */

        // Get holidays based on filter
        $holidays = $this->getHolidays($filter_type, $filter_value, $limit, $debug);

        if (empty($holidays)) {
            //$debug .= '</div></div>';
            return '<div class="alert alert-info">' . Text::_('No holidays found for the selected criteria.') . '</div>';
        }

        //$output = $debug . '</div></div>';
        $output = '<div class="sppb-addon sppb-addon-holidaygrid ' . $class . '">';
        $output .= '<div class="sppb-addon-content">';
        
        if ($title) {
            $output .= '<' . $heading_selector . ' class="sppb-addon-title">' . $title . '</' . $heading_selector . '>';
        }

        $output .= '<div class="holiday-grid-container">';
        $output .= '<div class="holiday-grid">';

        foreach ($holidays as $holiday) {
            $output .= $this->renderHolidayCard($holiday);
        }

        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';

        return $output;
    }

    private function getHolidays($filter_type, $filter_value, $limit, &$debug)
    {
        $db = Factory::getDbo();
        $query = $db->getQuery(true);

        // First, let's see what locations exist
        $debugQuery = $db->getQuery(true)
            ->select('id, name, alias, level, path')
            ->from('#__zenlocations')
            ->order('path ASC');

        // Debug the actual SQL
        $db->setQuery($debugQuery);
        if (defined('JDEBUG') && JDEBUG) {
            $debug .= 'Raw SQL for All Locations: ' . $db->getQuery() . '<br>';
            $allLocations = $db->loadObjectList();
            $debug .= 'All Locations found: ' . htmlspecialchars(json_encode($allLocations)) . '<br>';
        }

        // Debug locations_usage table
        $usageQuery = $db->getQuery(true)
            ->select('*')
            ->from('#__zenlocations_usage')
            ->where('item_type = ' . $db->quote('holiday'))
            ->where('extension = ' . $db->quote('com_zenholidays'));
        
        $db->setQuery($usageQuery);
        if (defined('JDEBUG') && JDEBUG) {
            $debug .= 'Raw SQL for Usage Check: ' . $db->getQuery() . '<br>';
            $usages = $db->loadObjectList();
            $debug .= 'Location Usages found: ' . htmlspecialchars(json_encode($usages)) . '<br>';
        }

        // Now try to find the specific locations we want
        $locationQuery = $db->getQuery(true)
            ->select('id, name, level, path')
            ->from('#__zenlocations');

        if ($filter_type === 'continent') {
            // For continents, look for level 1 locations
            $locationQuery->where('level = 1')
                ->where('name LIKE ' . $db->quote('%' . $filter_value . '%'));
        } elseif ($filter_type === 'country') {
            // For countries, look for level 2 locations
            $locationQuery->where('level = 2')
                ->where('name LIKE ' . $db->quote('%' . $filter_value . '%'));
        }

        // Debug the actual SQL
        $db->setQuery($locationQuery);
        if (defined('JDEBUG') && JDEBUG) {
            $debug .= 'Raw SQL for Location Query: ' . $db->getQuery() . '<br>';
            $locations = $db->loadObjectList();
            $debug .= 'Locations found: ' . htmlspecialchars(json_encode($locations)) . '<br>';
        }

        if (empty($locations)) {
            return array();
        }

        $locationIds = array_column($locations, 'id');

        // If we're filtering by continent, also get all countries within that continent
        if ($filter_type === 'continent' && !empty($locations)) {
            $continentPath = $locations[0]->path;
            $countryQuery = $db->getQuery(true)
                ->select('id')
                ->from('#__zenlocations')
                ->where('level = 2')
                ->where('path LIKE ' . $db->quote($continentPath . '/%'));
            
            $db->setQuery($countryQuery);
            if (defined('JDEBUG') && JDEBUG) {
                $debug .= 'Country Query: ' . $db->getQuery() . '<br>';
                $countryIds = $db->loadColumn();
                $debug .= 'Country IDs found: ' . htmlspecialchars(json_encode($countryIds)) . '<br>';
            }
            
            if (!empty($countryIds)) {
                $locationIds = array_merge($locationIds, $countryIds);
            }
        }

        // Debug the holidays state
        $holidayStateQuery = $db->getQuery(true)
            ->select('h.id, h.state')
            ->from('#__zenholidays h')
            ->join('INNER', '#__zenlocations_usage lu ON lu.item_id = h.id')
            ->where('lu.item_type = ' . $db->quote('holiday'))
            ->where('lu.extension = ' . $db->quote('com_zenholidays'))
            ->where('lu.location_id IN (' . implode(',', array_map('intval', $locationIds)) . ')');

        $db->setQuery($holidayStateQuery);
        if (defined('JDEBUG') && JDEBUG) {
            $debug .= 'Raw SQL for Holiday State Check: ' . $db->getQuery() . '<br>';
            $holidayStates = $db->loadObjectList();
            $debug .= 'Holiday States found: ' . htmlspecialchars(json_encode($holidayStates)) . '<br>';
        }

        // Now get holidays that belong to these locations
        $query->select('DISTINCT h.id')
            ->from('#__zenholidays h')
            ->join('INNER', '#__zenlocations_usage lu ON lu.item_id = h.id')
            ->where('h.state = 1')
            ->where('lu.item_type = ' . $db->quote('holiday'))
            ->where('lu.extension = ' . $db->quote('com_zenholidays'))
            ->where('lu.location_id IN (' . implode(',', array_map('intval', $locationIds)) . ')');

        $query->order('h.ordering ASC')
            ->setLimit($limit);

        // Debug the actual SQL
        $db->setQuery($query);
        if (defined('JDEBUG') && JDEBUG) {
            $debug .= 'Raw SQL for Holiday Query: ' . $db->getQuery() . '<br>';
            $ids = $db->loadColumn();
            $debug .= 'Holiday IDs found: ' . htmlspecialchars(json_encode($ids)) . '<br>';
        }

        if (empty($ids)) {
            return array();
        }

        $holidays = array();
        JModelLegacy::addIncludePath(JPATH_ROOT . '/components/com_zenholidays/models');
        $holidayModel = JModelLegacy::getInstance('Holiday', 'ZenHolidaysModel');

        foreach ($ids as $id) {
            $holiday = $holidayModel->getItem($id);
            if ($holiday) {
                $holidays[] = $holiday;
            }
        }

        return $holidays;
    }

    private function renderHolidayCard($holiday)
    {
        $app = Factory::getApplication();
        $template = JPATH_ROOT . '/templates/' . $app->getTemplate() . '/sppagebuilder/addons/holiday/tmpl.php';

        if (!file_exists($template)) {
            return '<div class="alert alert-warning">Please create a holiday template at: ' . $template . '</div>';
        }

        ob_start();
        include($template);
        $output = ob_get_contents();
        ob_end_clean();

        return $output;
    }
} 