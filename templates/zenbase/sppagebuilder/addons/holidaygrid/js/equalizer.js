(function($) {
    'use strict';

    function equalizeCardHeights() {
        const grid = document.querySelector('.sppb-addon-holidaygrid .holiday-grid');
        const cards = grid.querySelectorAll('.zen-card');
        
        if (!grid || !cards.length) return;

        // Handle mobile layout
        if (window.innerWidth <= 576) {
            grid.classList.add('mobile-layout');
            cards.forEach(card => {
                card.style.height = 'auto';
            });
            return;
        }

        // Handle desktop layout
        grid.classList.remove('mobile-layout');
        grid.classList.add('equalize-grid');
        
        // Reset heights
        cards.forEach(card => {
            card.style.height = '';
        });

        // Get max height
        let maxHeight = 0;
        cards.forEach(card => {
            const height = card.offsetHeight;
            maxHeight = Math.max(maxHeight, height);
        });

        // Set equal heights
        cards.forEach(card => {
            card.style.height = maxHeight + 'px';
        });
    }

    // Run on document ready and window resize
    $(document).ready(equalizeCardHeights);
    $(window).on('resize', equalizeCardHeights);
})(jQuery); 