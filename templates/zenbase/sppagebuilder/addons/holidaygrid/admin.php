<?php
/**
 * @package SP Page Builder
 * <AUTHOR> http://www.joomshaper.com
 * @copyright Copyright (c) 2010 - 2016 Jo<PERSON>Shaper
 * @license http://www.gnu.org/licenses/gpl-2.0.html GNU/GPLv2 or later
*/
//no direct accees
defined('_JEXEC') or die('restricted aceess');

use Joom<PERSON>\CMS\Factory;
use Joomla\CMS\Language\Text;

SpAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'holidaygrid',
        'title' => Text::_('Holiday Grid'),
        'desc' => Text::_('Display a grid of holiday cards filtered by continent or country'),
        'category' => 'Content',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => Text::_('COM_SPPAGEBUILDER_ADDON_ADMIN_LABEL'),
                    'desc' => Text::_('COM_SPPAGEBUILDER_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                'title' => array(
                    'type' => 'text',
                    'title' => Text::_('Title'),
                    'desc' => Text::_('Enter a title for the holiday grid'),
                    'std' => ''
                ),
                'heading_selector' => array(
                    'type' => 'select',
                    'title' => Text::_('Heading Selector'),
                    'desc' => Text::_('Select the heading selector for the title'),
                    'values' => array(
                        'h1' => Text::_('H1'),
                        'h2' => Text::_('H2'),
                        'h3' => Text::_('H3'),
                        'h4' => Text::_('H4'),
                        'h5' => Text::_('H5'),
                        'h6' => Text::_('H6')
                    ),
                    'std' => 'h3'
                ),
                'filter_type' => array(
                    'type' => 'select',
                    'title' => Text::_('Filter Type'),
                    'desc' => Text::_('Select how to filter the holidays'),
                    'values' => array(
                        'continent' => Text::_('By Continent'),
                        'country' => Text::_('By Country')
                    ),
                    'std' => 'continent'
                ),
                'filter_value' => array(
                    'type' => 'text',
                    'title' => Text::_('Filter Value'),
                    'desc' => Text::_('Enter the continent or country name to filter by'),
                    'std' => ''
                ),
                'limit' => array(
                    'type' => 'number',
                    'title' => Text::_('Number of Holidays'),
                    'desc' => Text::_('Maximum number of holidays to display'),
                    'std' => 12
                ),
                'class' => array(
                    'type' => 'text',
                    'title' => Text::_('Custom Class'),
                    'desc' => Text::_('Enter custom CSS class'),
                    'std' => ''
                )
            )
        )
    )
); 