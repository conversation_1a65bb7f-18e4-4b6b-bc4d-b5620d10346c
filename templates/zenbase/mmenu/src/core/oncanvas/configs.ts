const configs: mmConfigs = {
    classNames: {
        divider: 'Divider',
        nolistview: 'NoListview',
        nopanel: 'NoPanel',
        panel: 'Panel',
        selected: 'Selected',
        vertical: 'Vertical'
    },
    language: null,
    panelNodetype: ['ul', 'ol', 'div'],
    screenReader: {
      closeSubmenu: 'Close submenu',
      openSubmenu: 'Open submenu',
      toggleSubmenu: 'Toggle submenu'
    }
};
export default configs;
