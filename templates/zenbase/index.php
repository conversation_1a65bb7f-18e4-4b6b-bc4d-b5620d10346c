<?php
  /**
   * @package     Joomla.Site
   * @subpackage  Templates.protostar
   *
   * @copyright   Copyright (C) 2005 - 2015 Open Source Matters, Inc. All rights reserved.
   * @license     GNU General Public License version 2 or later; see LICENSE.txt
   */

  defined('_JEXEC') or die;

  use <PERSON><PERSON><PERSON>\CMS\Helper\ModuleHelper;

  jimport('mrzen.utils');
  jimport('mrzen.helpers.ZenAngularHelper');
  jimport('mrzen.models.helper');
  include_once (JPATH_BASE . '/templates/zenbase/template.php');

  $isHome = false;
  $template = new ZenbaseTemplateHelper($this);
  $template_url = $this->baseurl . '/templates/' . $this->template;

  $app = JFactory::getApplication();
  $doc = JFactory::getDocument();
  $uri = JUri::getInstance();
  $user = JFactory::getUser();
  $params = &$app->getParams();
  $this->language = $doc->language;
  $this->direction = $doc->direction;

  // Getting parameters from the template.
  //
  $templateParams = $this->params;

  // Fetch the values of bookingEnabled and bookingDisabledRedirect from the template parameters
  $bookingEnabled = $templateParams->get('bookingenabled');
  $bookingDisabledRedirect = $templateParams->get('bookingdisabledredirect');

  // Breadcrumb Module.
  //
  $breadcrumbsModule = JModuleHelper::getModule('mod_breadcrumbs');
  $breadcrumbsParams = new JRegistry($breadcrumbsModule->params);
  $breadcrumbs_sfx = $breadcrumbsParams->get('moduleclass_sfx');

  // Page Classes.
  //
  $pageclass_sfx = $params->get('pageclass_sfx');
  $page_classes = (strpos($pageclass_sfx, 'homepage') === false) ? 'subpage' : '';
  $pageclass_sfx .= $page_classes;

  // Add JavaScript Frameworks.
  //
  JHtml::_('jquery.framework');

  // Optional CSS Classes based on user agent.
  //
  $doc->addScript($template_url . '/js/css_browser_selector.js');
  $doc->addScript($template_url . '/js/popper.min.js');
  $doc->addScript($template_url . '/js/bootstrap/bootstrap.min.js');
  $doc->addScript($template_url . '/js/styledSelects.js');
  $doc->addScript($template_url . '/js/slick.min.js');
  $doc->addScript($template_url . '/js/deep-link-to-tabs-v2.js');
  $doc->addScript($template_url . '/js/deep-link-button-handlers.js');
  $doc->addScript($template_url . '/js/additional.js');
  $doc->addScript($template_url . '/js/template.js');
  $doc->addScript($template_url . '/js/preview-bullets-events.js');

  // Add Stylesheets.
  //
  $doc->addStyleSheet($template_url . '/css/bootstrap.css');
  $doc->addStyleSheet($template_url . '/css/'.$templateParams->get('sitestyle'));
  $doc->addStyleSheet($template_url . '/css/preview-bullets.css');

  // Set canonical link.
  //
  //==================================================================
  // WARNING: Check all page types for any potential SEO issues.
  // DESCRIPTION: This feature will remove parameters at the end of
  // the URL, including multi-page pagination on news articles and
  // search pages. For example all the search filters and type views
  // will go to the main search web address.
  //==================================================================
  //
  $canonicalItm = [];
  $websiteAddrs = $_SERVER['REQUEST_URI'];

  $regexPattern = "([^\?]*)";
  preg_match($regexPattern, $websiteAddrs, $canonicalItm);
  $canonical = '<link href="'.$uri->toString([scheme]).$uri->toString([host]).rtrim($canonicalItm[0], '/').'" rel="canonical" />';
  $doc->addCustomTag($canonical);
  // Check if home page.
  //
  if (strpos($pageclass_sfx, 'homepage') !== false) {
    $isHome = true;
  }
?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="<?php echo $this->language; ?>" lang="<?php echo $this->language; ?>" dir="<?php echo $this->direction; ?>">
  <head>
    <style>
      body:not(.mm-wrapper) #mobile-menu {
        display: none;
      }

      .mm-menu {
        --mm-color-background: #333;
        --mm-color-text: #fff;
        --mm-color-button: #fff;
      }

      #mobile-menu {
        background: var(--mm-color-background);
        color: var(--mm-color-text);
      }
    </style>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <link rel="apple-touch-icon" sizes="180x180" href="<?= $template_url; ?>/images/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="<?= $template_url; ?>/images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="<?= $template_url; ?>/images/favicon-16x16.png">
    <link rel="manifest" href="<?= $template_url; ?>/images/site.webmanifest">
    <link rel="mask-icon" href="<?= $template_url; ?>/images/safari-pinned-tab.svg" color="#0095cc">
    <?php include_once (JPATH_BASE . '/templates/zenbase/html/partials/preload.php'); ?>
    <meta name="msapplication-TileColor" content="#2b5797">
    <meta name="theme-color" content="#ffffff">
    <jdoc:include type="head" />
    <link rel="stylesheet" href="<?php echo $this->baseurl; ?>/templates/<?php echo $this->template; ?>/css/overrides.css?v=<?php echo uniqid(); ?>" />
    <?php // Get the robots setting from the global configuration
      $app = JFactory::getApplication();
      $params = $app->getParams();
      $robots = $params->get('robots', 'index, follow'); // Default to 'index, follow' if not set
      echo '<meta name="robots" content="' . htmlspecialchars($robots) . '" />';
      ?>

    <?php if($templateParams->get('usegtmtag') == 1 ) : ?>
      <!-- Google Tag Manager -->
      <script>
        <?php echo $templateParams->get('gtmhead'); ?>
      </script>
    <!-- End Google Tag Manager -->
    <!-- Adding GA4 Initialization -->
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
    </script>
    <!-- End GA4 Initialization -->
    <?php endif; ?>
    <!-- mmenu CSS -->
    <link rel="stylesheet" href="<?php echo $this->baseurl; ?>/templates/zenbase/mmenu/dist/mmenu.css" />
    <link rel="stylesheet" href="<?php echo $this->baseurl; ?>/templates/zenbase/css/mmenu-custom.css" />
    <link rel="stylesheet" href="<?php echo $this->baseurl; ?>/templates/zenbase/css/menu-icons.css" />

    <script>
      jQuery(function() {
        // Video modal handling
        const videoModal = document.getElementById('videoLightbox');
        if (videoModal) {
          const videoIframe = videoModal.querySelector('iframe');
          let videoTime = 0;

          // Function to detect mobile devices
          function isMobileDevice() {
            return window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
          }

          videoModal.addEventListener('hidden.bs.modal', function () {
            // Store current time
            if (videoIframe.contentWindow) {
              try {
                videoTime = videoIframe.contentWindow.postMessage('{"event":"command","func":"getCurrentTime","args":""}', '*');
              } catch (e) {
                console.log('Could not get video time');
              }
            }
            // Stop video
            const videoSrc = videoIframe.src;
            videoIframe.src = videoSrc;
          });

          videoModal.addEventListener('show.bs.modal', function () {
            // Add autoplay parameter for mobile devices only
            if (isMobileDevice() && videoIframe) {
              let currentSrc = videoIframe.src;
              // Check if URL already has parameters
              const separator = currentSrc.includes('?') ? '&' : '?';
              // Add autoplay=1 and mute=1 if not already present
              if (!currentSrc.includes('autoplay=')) {
                currentSrc += separator + 'autoplay=1&mute=1';
              }
              // Only reload if the URL has changed
              if (currentSrc !== videoIframe.src) {
                videoIframe.src = currentSrc;
              }
            } else if (videoIframe) {
              // Remove autoplay and mute parameters for desktop
              let currentSrc = videoIframe.src;
              currentSrc = currentSrc.replace(/[?&](autoplay=1|mute=1)/g, '');
              // Clean up any double ampersands or trailing question marks
              currentSrc = currentSrc.replace(/[?&]&/g, '&').replace(/[?&]$/, '');
              if (currentSrc !== videoIframe.src) {
                videoIframe.src = currentSrc;
              }
            }

            // Restore video time if it exists
            if (videoTime > 0) {
              try {
                videoIframe.contentWindow.postMessage('{"event":"command","func":"seekTo","args":[' + videoTime + ',true]}', '*');
              } catch (e) {
                console.log('Could not seek to time');
              }
            }
          });
        }
      });
    </script>
  </head>
  <body class="<?= $pageclass_sfx; ?> p-0">
    <?php if($templateParams->get('usegtmtag') == 1 ) : ?>
      <!-- Google Tag Manager (noscript) -->
      <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=<?php echo $templateParams->get('gtmbody'); ?>"
      height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
      <!-- End Google Tag Manager (noscript) -->
    <?php endif; ?>

    <!-- Mobile Menu -->
    <div id="mobile-menu" class="mm-menu">
      <?php if ($this->countModules('mainMenuMobile')) : ?>
        <jdoc:include type="modules" name="mainMenuMobile" style="none" />
      <?php endif; ?>
    </div>

    <!-- Page wrapper -->
    <div id="page" class="zen-page">
      <div class="zen-overlay"></div>
      <!------------------------------------------------------------------------------
      // Site Notice
      //----------------------------------------------------------------------------->
      <?php if ($this->countModules('topNotice')) : ?>
        <jdoc:include type="modules" name="topNotice" style="none" />
      <?php endif; ?>

      <div class="zen-header-container">
        <div class="zen-utility-bar">
          <div class="top-whatsapp d-flex d-md-none">
            <a href="https://api.whatsapp.com/send/?phone=%2B447897030466" class="fg-white text-decoration-none"><img src="/templates/zenbase/icons/whatsapp.svg" alt=""> WhatsApp us</a>
          </div>
          <div class="top-social d-none d-md-flex">
            <a href="https://www.facebook.com/evertrekuk/" title="Facebook" target="_blank"><img src="<?= $template_url; ?>/icons/social-facebook.svg" alt="Facebook"></a>
            <a href="https://www.instagram.com/evertrekuk/" title="Instagram" target="_blank"><img src="<?= $template_url; ?>/icons/social-instagram.svg" alt="Instagram"></a>
            <a href="https://www.youtube.com/@EverTrekUK" title="Youtube" target="_blank"><img src="<?= $template_url; ?>/icons/social-youtube.svg" alt="Youtube"></a>
            <a href="https://twitter.com/evertrekuk" title="Twitter" target="_blank"><img src="<?= $template_url; ?>/icons/social-twitter.svg" alt="Twitter"></a>
          </div>
          <div class="top-social d-flex d-md-none">
            <a href="https://www.facebook.com/evertrekuk/" title="Facebook"><img src="<?= $template_url; ?>/icons/social-facebook.svg" alt="Facebook"></a>
            <a href="https://www.instagram.com/evertrekuk/" title="Instagram"><img src="<?= $template_url; ?>/icons/social-instagram.svg" alt="Instagram"></a>
            <a href="https://www.youtube.com/@EverTrekUK" title="Youtube"><img src="<?= $template_url; ?>/icons/social-youtube.svg" alt="Youtube"></a>
            <a href="https://twitter.com/evertrekuk" title="Twitter"><img src="<?= $template_url; ?>/icons/social-twitter.svg" alt="Twitter"></a>
          </div>

          <div class="top-ratings d-none d-md-flex">
            <a class="fg-white text-decoration-none" href="https://uk.trustpilot.com/review/bucketlistadventuretravel.co.uk" target="_blank">
              <span>Excellent</span>
              <span><strong>4.7</strong> out of 5</span>
              <img src="<?= $template_url; ?>/icons/trustpilot.svg" alt="Trustpilot">
            </a>
          </div>
          <div class="top-login">
            <a href="https://evertrek-booking.vercel.app/" class="fg-white zen-link zen-link--no-underline">Login <img src="<?= $template_url; ?>/icons/login.svg" alt="Login"></a>
          </div>
        </div>

        <?php $module = ModuleHelper::getModule('mod_php_js', 'Campaign: Top Bar');
   echo ModuleHelper::renderModule($module); ?>

        <!------------------------------------------------------------------------------
        // Header
        //----------------------------------------------------------------------------->
        <header class="zen-header">
          <div class="zen-header__main">
            <div class="container">
            <div class="mx-auto">
              <div class="nav-wrapper mx-auto">
                <div class="zen-header__logo">
                  <?php if ($this->countModules('headerMainLeft')) : ?>
                    <jdoc:include type="modules" name="headerMainLeft" style="none" />
                  <?php endif; ?>
                </div>
                <div class="zen-header__menu">
                  <!------------------------------------------------------------------------------
                  // Menu
                  //----------------------------------------------------------------------------->
                  <div class="zen-menu justify-content-end align-items-center">
                    <div class="zen-menu__main">
                      <?php if ($this->countModules('mainMenu')) : ?>
                        <div class="d-none d-xl-block justify-content-end align-items-center h-100">
                          <jdoc:include type="modules" name="mainMenu" style="none" />
                        </div>
                      <?php endif;?>
                    </div>
                  </div>
                </div>
                <div class="zen-header__info col-md-2 pe-0"> <!-- col-6 col-lg-2 ps-0 -->
                <div class="zen-header__search d-block d-xl-none">
                  <a class="zen-link zen-link--no-underline" href="#" data-bs-toggle="offcanvas" data-bs-target="#searchOffcanvas">
                    <!-- <i class="zen-icon zen-icon--text-sm fontello icon-sys-search me-3 me-xl-3"></i> -->
                  </a>
                </div>

                <!-- Offcanvas for keyword search -->
                <div class="offcanvas offcanvas-end" tabindex="-1" id="searchOffcanvas">
                  <div class="offcanvas-header">
                    <h5 class="offcanvas-title">Search</h5>
                    <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
                  </div>
                  <div class="offcanvas-body">
                    <form id="offcanvasSearchForm">
                      <div class="mb-3">
                        <input type="text" class="form-control" id="offcanvasSearchInput" placeholder="Enter search text">
                      </div>
                      <button type="submit" class="zen-btn zen-btn--primary zen-btn--full-size">Search</button>
                    </form>
                  </div>
                </div>

                  <div class="zen-flex-end d-flex">
                    <a class="zen-cta zen-cta--text-xl py-1 px-1" id="mobile-menu-trigger">
                      <i class="zen-icon zen-icon--text-lg fontello icon-sys-hamburger" aria-hidden="true"></i>
                    </a>
                  </div>
                </div>
              </div>
            </div>
            </div>
          </div>
          <span class="sticky-marker"></span>
        </header>
      </div>

      <!------------------------------------------------------------------------------
      // Body Content - Including Breadcrumbs
      //----------------------------------------------------------------------------->
      <div class="zen-body">
        <?php if (!$this->countModules('breadcrumbs') || strpos($breadcrumbs_sfx, 'light-breadcrumbs') !== false): ?>
          <?php $breadcrumbsUnset = "pt-0"; ?>
        <?php endif; ?>
        <?php if ($this->countModules('breadcrumbs') && !$isHome) : ?>
          <div class="zen-body__breadcrumbs <?= $breadcrumbsClass; ?> ">
            <div class="container-fluid">
              <div class="row">
                <div class="col-12">
                  <jdoc:include type="modules" name="breadcrumbs" style="xhtml" />
                </div>
              </div>
            </div>
          </div>
        <?php endif; ?>
        <div class="zen-body__main <?= $breadcrumbsUnset; ?>">
          <main id="content" role="main" class="">
            <jdoc:include type="message" />
            <jdoc:include type="component" />
          </main>

        </div>
      </div>

<style>
 /* GLOBAL */

                /* OFFER BAR */
                .light-link { text-decoration:none; border-bottom: 1px solid rgba(0,0,0,.25); padding-bottom:0; margin-bottom:0;  }
                .fg-black { color: black; }
                .bg-yellow { background-color: #ffc105; }
                .zen-header strong { font-family: "IBM Plex Sans Semibold"; font-weight: 400; }
                /*.zen-body__main { padding: 36px 0 0; }*/


                /* MOBILE BAR */

                .mobile-bar {
                  position:fixed; bottom: 0; left:0; right: 0; display:flex; align-items: center;justify-content:space-around;
                  box-shadow: 0 1px 10px 0 rgba(38, 38, 38, 0.3);
                  background-color: rgba(255, 255, 255, 1);
                  transition:bottom .1s ease-in-out;
                  z-index: 9999999999;
                }
                .mobile-bar div {
                  display: flex; flex-direction: column; align-items: center;
                }
                .mobile-bar a { text-decoration: none; }
                .mobile-bar a:focus svg, .mobile-bar a:active svg { color: #999; }
                .mobile-bar svg { height: 40px; color: #525252; transition: .25s color; }
                .mobile-bar span { color: #8c8c8c; display:block; margin-top: 5px; }
                .mobile-bar a[disabled] { opacity: 0.5; pointer-events: none;}
                .mobile-bar-show { display:block }
                .mobile-bar-hide { display: none; bottom:-100px }
</style>

      <div class="mobile-bar py-3 d-flex d-sm-none">
            <a class="mobile-brochure-button" href="/about-us/download-our-adventure-brochure">
              <div><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 83 61"><g fill="currentColor"><path d="M52.311 52.038a.954.954 0 0 1-.79 1.127c-7.887 1.113-11.887 3.347-14.38 5.936l-.48.193a.584.584 0 0 1-.481-.579V11.023c0-.579.288-1.253.77-1.638a20.215 20.215 0 0 1 4.522-2.891 51.908 51.908 0 0 1 21.746-3.757l.098-.001c2.427 0 4.425 2 4.425 4.431v24.915a.955.955 0 0 1-.89.95c-8.255.59-14.834 7.57-14.834 15.95 0 1.026.098 2.05.294 3.056ZM26.269 6.397a20.207 20.207 0 0 1 4.618 2.892c.482.481.77 1.155.77 1.734v47.691c0 .29-.288.58-.578.58-.192 0-.288 0-.48-.194-5.966-5.685-16.07-6.647-26.077-6.647C1.924 52.453 0 50.428 0 47.634V7.168c0-1.445.77-2.89 1.924-3.66a5.761 5.761 0 0 1 2.694-.771c9.623 0 16.647 1.252 21.747 3.66h-.096ZM66.2 50.449v-6.283l-.002-.071c0-.74.608-1.349 1.349-1.349a1.36 1.36 0 0 1 1.346 1.42V50.5l2.235-2.19a1.35 1.35 0 0 1 .942-.383c.739 0 1.347.608 1.347 1.347 0 .363-.146.71-.405.965L68.49 54.67a1.358 1.358 0 0 1-1.895-.01l-4.426-4.433a1.349 1.349 0 0 1-.395-.954c0-.739.608-1.347 1.348-1.347.357 0 .7.142.953.394l2.125 2.13Z"/><path d="M67.548 36.94c6.571 0 11.98 5.416 11.98 11.996s-5.409 11.996-11.98 11.996c-6.571 0-11.98-5.415-11.98-11.996 0-6.58 5.408-11.996 11.98-11.996Zm0 2.698c-5.094 0-9.286 4.197-9.286 9.298 0 5.1 4.192 9.298 9.286 9.298 5.094 0 9.285-4.197 9.285-9.298 0-5.1-4.191-9.298-9.285-9.298Z"/></g></svg><span>Brochure</span></div>
            </a>
            <a class="mobile-chat-button" href="#" disabled>
              <div><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M398 81.84A227.4 227.4 0 0 0 255.82 32C194.9 32 138 55.47 95.46 98.09C54.35 139.33 31.82 193.78 32 251.37a215.66 215.66 0 0 0 35.65 118.76l.19.27c.28.41.57.82.86 1.22s.65.92.73 1.05l.22.4c1.13 2 2 4.44 1.23 6.9l-18.42 66.66a29.13 29.13 0 0 0-1.2 7.63A25.69 25.69 0 0 0 76.83 480a29.44 29.44 0 0 0 10.45-2.29l67.49-24.36l.85-.33a14.75 14.75 0 0 1 5.8-1.15a15.12 15.12 0 0 1 5.37 1c1.62.63 16.33 6.26 31.85 10.6c12.9 3.6 39.74 9 60.77 9c59.65 0 115.35-23.1 156.83-65.06C457.36 365.77 480 310.42 480 251.49a213.5 213.5 0 0 0-4.78-45c-10.34-48.62-37.76-92.9-77.22-124.65M160 288a32 32 0 1 1 32-32a32 32 0 0 1-32 32m96 0a32 32 0 1 1 32-32a32 32 0 0 1-32 32m96 0a32 32 0 1 1 32-32a32 32 0 0 1-32 32"/></svg><span>Live Chat</span></div>
            </a>
            <a class="mobile-callback-button" href="/call-me-back">
              <div><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M391 480c-19.52 0-46.94-7.06-88-30c-49.93-28-88.55-53.85-138.21-103.38C116.91 298.77 93.61 267.79 61 208.45c-36.84-67-30.56-102.12-23.54-117.13C45.82 73.38 58.16 62.65 74.11 52a176.3 176.3 0 0 1 28.64-15.2c1-.43 1.93-.84 2.76-1.21c4.95-2.23 12.45-5.6 21.95-2c6.34 2.38 12 7.25 20.86 16c18.17 17.92 43 57.83 52.16 77.43c6.15 13.21 10.22 21.93 10.23 31.71c0 11.45-5.76 20.28-12.75 29.81c-1.31 1.79-2.61 3.5-3.87 5.16c-7.61 10-9.28 12.89-8.18 18.05c2.23 10.37 18.86 41.24 46.19 68.51s57.31 42.85 67.72 45.07c5.38 1.15 8.33-.59 18.65-8.47c1.48-1.13 3-2.3 4.59-3.47c10.66-7.93 19.08-13.54 30.26-13.54h.06c9.73 0 18.06 4.22 31.86 11.18c18 9.08 59.11 33.59 77.14 51.78c8.77 8.84 13.66 14.48 16.05 20.81c3.6 9.53.21 17-2 22c-.37.83-.78 1.74-1.21 2.75a176.49 176.49 0 0 1-15.29 28.58c-10.63 15.9-21.4 28.21-39.38 36.58A67.42 67.42 0 0 1 391 480"/></svg><span>Call me back</span></div>
            </a>
            </div>

    <!------------------------------------------------------------------------------
    // Footer
    //----------------------------------------------------------------------------->
    <?php if ($this->countModules('uspSection')) : ?>
      <div class="zen-bg-white">
        <jdoc:include type="modules" name="uspSection" style="none" />
      </div>
    <?php endif; ?>

    <?php if ($this->countModules('contact')) : ?>
      <div class="zen-bg-light px-3">
        <jdoc:include type="modules" name="contact" style="none" />
      </div>
    <?php endif; ?>
    <!------------------------------------------------------------------------------
    // Footer
    //----------------------------------------------------------------------------->
    <footer class="zen-footer">
      <?php if ($this->countModules('footer')) : ?>
        <jdoc:include type="modules" name="footer" style="none" />
      <?php endif; ?>
      <div class="container mb-4">
        <div class="row">
          <?php if ($this->countModules('footerMenu')) : ?>
            <div class="col-12">
              <div class="zen-footer__menu">
                 <jdoc:include type="modules" name="footerMenu" style="none" />
              </div>
            </div>
          <?php endif; ?>
        </div>
      </div>
    </footer>

    </div><!-- /#page -->

    <!------------------------------------------------------------------------------
    // Modals - Placed outside page wrapper for proper stacking
    //---------------------------------------------------------------------------->
    <div id="modals" class="zen-modals">
      <?php if ($this->countModules('modal')): ?>
        <jdoc:include type="modules" name="modal" style="none" />
      <?php endif;?>

      <div class="modal modal-brochure fade"
      id="brochureModal"
      tabindex="3"
      role="dialog"
      aria-hidden="true"
      >
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-body p-3">
              <button type="button" class="zen-btn mb-2" data-bs-dismiss="modal">&times;</button>
                <jdoc:include type="modules" name="brochureModal" style="none" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!------------------------------------------------------------------------------
    // Back to Top Button
    //---------------------------------------------------------------------------->
    <div class="zen-back-to-top">
      <button class="zen-btn zen-btn--scroll js-zen-back-to-top">
        <em class="fontello icon-chevron-up"></em>
      </button>
    </div>

    <!------------------------------------------------------------------------------
    // Cookie Policy
    //---------------------------------------------------------------------------->
    <div class="zen-panel zen-panel--policy" style="display: none;">
      <div class="container">
        <div class="row">
          <div class="col-12">
            <p class="zen-text zen-text--lead mb-2">
              <?php echo JText::_('ZEN_SITE_COOKIE_TITLE'); ?>
              <span class="zen-cta float-right p-1 mt-n1 js-cookie-policy">
                <em class="fontello icon-sys-close"></em>
              </span>
            </p>
          </div>
          <div class="col-12">
            <div class="zen-panel__content">
              <div class="row">
                <div class="col-sm-9 col-md-10 d-inline-block">
                  <p class="zen-text mb-2">
                    <?php echo JText::_('ZEN_SITE_COOKIE_TEXT'); ?>
                  </p>
                </div>
                <div class="col-sm-3 col-md-2 d-inline-block">
                  <p class="mb-0">
                    <button class="zen-btn zen-btn--full-size zen-btn--text-xs px-3 js-cookie-policy">
                      <?php echo JText::_('ZEN_SITE_COOKIE_BTN'); ?>
                    </button>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Start of LiveChat (www.livechat.com) code -->
    <script>
        window.__lc = window.__lc || {};
        window.__lc.license = 18020283;
        window.__lc.integration_name = "manual_channels";
        window.__lc.product_name = "livechat";
        ;(function(n,t,c){function i(n){return e._h?e._h.apply(null,n):e._q.push(n)}var e={_q:[],_h:null,_v:"2.0",on:function(){i(["on",c.call(arguments)])},once:function(){i(["once",c.call(arguments)])},off:function(){i(["off",c.call(arguments)])},get:function(){if(!e._h)throw new Error("[LiveChatWidget] You can't use getters before load.");return i(["get",c.call(arguments)])},call:function(){i(["call",c.call(arguments)])},init:function(){var n=t.createElement("script");n.async=!0,n.type="text/javascript",n.src="https://cdn.livechatinc.com/tracking.js",t.head.appendChild(n)}};!n.__lc.asyncInit&&e.init(),n.LiveChatWidget=n.LiveChatWidget||e}(window,document,[].slice))
    </script>
    <noscript><a href="https://www.livechat.com/chat-with/18020283/" rel="nofollow">Chat with us</a>, powered by <a href="https://www.livechat.com/?welcome" rel="noopener nofollow" target="_blank">LiveChat</a></noscript>
    <!-- End of LiveChat code -->
    <script>
              // ON LOAD

              function etOnReady(initialData) {

                // Chat Widget is ready

                var state = initialData.state;
                var customerData = initialData.customerData;

                /* Hide greeting */
                window.LiveChatWidget.call("hide_greeting");

                // Change mobile bar chat button from inactive to active
                document.querySelector('.mobile-bar .mobile-chat-button').removeAttribute('disabled');

                document.querySelector('.mobile-chat-button').addEventListener('click', function(e) {
                  e.preventDefault();
                  var state = window.LiveChatWidget.get("state");
                  if(state.visibility === 'maximized') {
                    // Chat is active
                    window.LiveChatWidget.call("minimize");
                    // Allow mobile bar to show if needed
                  } else {
                    // Chat is inactive
                    window.LiveChatWidget.call("maximize");
                    // Hide mobile-bar and keep it hidden
                    document.querySelector('.mobile-bar').classList.add('mobile-bar-hide');
                    document.querySelector('.mobile-bar').classList.remove('mobile-bar-show');
                  }
              });
      }
      window.LiveChatWidget.on('ready', etOnReady);

    jQuery(function() {
/* CHAT */
        /* chat functions for mobile bar */
        /* https://help.gohighlevel.com/support/solutions/articles/48001191051-web-chat-widget-advanced-configurations-public-api-events */

        /* SCROLL FUNCTIONS */

        var mobileBarElement = document.querySelector('.mobile-bar');
        window.mobileBar = window.mobileBar || {};
        if (mobileBarElement !== null) {
            window.mobileBar.status = (window.getComputedStyle(mobileBarElement).display == 'block' || window.getComputedStyle(mobileBarElement).display == 'flex') ? 'open' : 'closed';
        }


        /* Hide header on scroll down, show on scroll up */
        /* Credit: https://twitter.com/mariusc23 */
        // Hide Header on on scroll down
        var didScroll,
            lastScrollTop = 0,
            delta = 5,
            windowInnerHeight = window.innerHeight,
            mobileBarHeight = jQuery('.mobile-bar').outerHeight();

        jQuery(window).scroll(function (event) {
            didScroll = true;
        });

        setInterval(function () {
            if (didScroll) {
                hasScrolled();
                didScroll = false;
            }
        }, 250);


        function hasScrolled() {
            var st = jQuery(this).scrollTop();

            if (jQuery('.mobile.bar').is(':visible')) {
                jQuery('.mobile-bar').removeClass('mobile-bar-show').addClass('mobile-bar-hide'); //hide mobile bar
                const event = new Event('mobilebar:hide');
                window.dispatchEvent(event);
                window.mobileBar = window.mobileBar || {};
                window.mobileBar.status = 'closed';
            } else {
                if (st > lastScrollTop) {
                    // Scroll Down
                    // Show bar
                    if (!jQuery('.mobile-bar').hasClass('mobile-bar-show') &&
                        typeof window.LiveChatWidget !== 'undefined' &&
                        window.LiveChatWidget._h) { // Check if widget is loaded

                        try {
                            var state = window.LiveChatWidget.get("state");
                            if (state.visibility === 'minimized') {
                                jQuery('.mobile-bar').addClass('mobile-bar-show').removeClass('mobile-bar-hide');
                                const event = new Event('mobilebar:show');
                                window.dispatchEvent(event);
                                window.mobileBar = window.mobileBar || {};
                                window.mobileBar.status = 'open';
                            }
                        } catch (e) {
                            console.debug('LiveChat not ready yet');
                        }
                    }
                } else {
                    // Scroll Up
                    // Hide bar - only if chat window not open
                    jQuery('.mobile-bar').removeClass('mobile-bar-show').addClass('mobile-bar-hide');
                    const event = new Event('mobilebar:hide');
                    window.dispatchEvent(event);
                    window.mobileBar = window.mobileBar || {};
                    window.mobileBar.status = 'closed';
                }
            }

            if (Math.abs(lastScrollTop - st) <= delta)
                return;

            lastScrollTop = st;
        }

      /* MENU FIXES */
                document.querySelectorAll('.zen-menu__main .nav-item a[href*="/destinations-menu"]').forEach(link => {
                  // console.log(link);
                  link.href = link.href.replace('destinations-menu','destinations')
                });
                      // Removing the event listener that prevents collection links from working
	});

  function appendQueryParamsToLinks(linkSelector) {
            const links = document.querySelectorAll(linkSelector);
            if (!links.length) return;

            const currentUrl = new URL(window.location.href);

            links.forEach(link => {
                const linkUrl = new URL(link.href);

                currentUrl.searchParams.forEach((value, key) => {
                  if (key.startsWith('utm') || key.startsWith('gad') || key.startsWith('gclid')) {
                        linkUrl.searchParams.set(encodeURIComponent(key), encodeURIComponent(value));
                    }
                });

                link.href = linkUrl.toString();
            });
        }

        document.addEventListener('DOMContentLoaded', function() {
            const currentUrlPath = window.location.pathname;
            if (currentUrlPath.startsWith('/ppc-landing')) {
                appendQueryParamsToLinks('.ppc-brochure');
            }
        });
</script>
<style>
.zen-holiday__tabs {
top: 107px;
}
</style>
    <!------------------------------------------------------------------------------
    // Debug Area
    //----------------------------------------------------------------------------->
    <jdoc:include type="modules" name="debug" style="none" />
    <script src="<?= $template_url; ?>/js/zen-lazy-load.js" type="text/javascript"></script>

    <!-- mmenu JavaScript -->
    <script src="<?php echo $this->baseurl; ?>/templates/zenbase/mmenu/dist/mmenu.js"></script>
    <script>
      document.addEventListener('DOMContentLoaded', () => {
        const menu = new Mmenu('#mobile-menu', {
          // Core options
          slidingSubmenus: true,
          // Off-canvas configuration
          offCanvas: {
            position: 'right-front'
          },
          "theme": "dark",
          // Navigation configuration
          navbar: {
            title: 'Menu'
          },
          navbars: [{
            position: 'top',
            content: ['prev', 'title']
          }],
          // Visual configuration
          extensions: [
            'pagedim-black',
            'position-right',
            'fx-menu-slide'
          ],
          // Disable automatic panel linking for specific items
          onClick: {
            preventDefault: function(anchor) {
              // Check if this is a Destinations or Collections item
              return anchor.parentElement.classList.contains('no-click');
            }
          }
        }, {
          // Configuration object
          offCanvas: {
            page: {
              selector: '#page'
            }
          },
          classNames: {
            selected: "Selected"
          }
        });

        // Get the API
        const api = menu.API;

        // Add click handler for the menu trigger
        document.getElementById('mobile-menu-trigger').addEventListener('click', (e) => {
          e.preventDefault();
          api.open();
        });
      });
    </script>
  </body>
</html>
