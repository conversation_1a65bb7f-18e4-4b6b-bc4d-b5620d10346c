<?php
/**
 * Test script for preview bullets functionality
 * 
 * This script tests the preview bullets functionality directly without going through the Joomla framework.
 * Place this file in the root directory of your Joomla installation and access it via the browser.
 */

// Define Joomla constants to prevent errors
define('_JEXEC', 1);
define('JPATH_BASE', __DIR__);
define('DS', DIRECTORY_SEPARATOR);

// Include Joomla framework
require_once JPATH_BASE . '/includes/defines.php';
require_once JPATH_BASE . '/includes/framework.php';

// Initialize Joomla application
$app = JFactory::getApplication('site');
$db = JFactory::getDbo();

// Get a sample trip ID from the database
$query = $db->getQuery(true)
    ->select('id, name, alias')
    ->from('#__zenholidays')
    ->where('state = 1')
    ->order('id ASC')
    ->setLimit(5);
$db->setQuery($query);
$trips = $db->loadObjectList();

// Import the ZenModelHelper class
jimport('mrzen.models.helper');

// Function to get preview bullets for a trip
function getPreviewBullets($tripId) {
    if (!class_exists('ZenModelHelper')) {
        return ['success' => false, 'message' => 'ZenModelHelper class not found'];
    }
    
    try {
        $previewBullets = ZenModelHelper::getCopyItemsByAlias('com_zenholidays', $tripId, 'preview-bullets');
        
        if ($previewBullets && isset($previewBullets['preview-bullets']) && isset($previewBullets['preview-bullets']->items)) {
            $output = '';
            foreach ($previewBullets['preview-bullets']->items as $bullet) {
                $output .= $bullet->content;
            }
            return ['success' => true, 'data' => $output];
        } else {
            return ['success' => false, 'message' => 'No preview bullets found for trip ID: ' . $tripId];
        }
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Error: ' . $e->getMessage()];
    }
}

// Output HTML
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html>
<head>
    <title>Preview Bullets Direct Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            padding: 0;
        }
        .test-card {
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .test-card h3 {
            margin-top: 0;
        }
        .preview-bullets {
            margin: 15px 0;
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 5px;
        }
        .debug-info {
            font-family: monospace;
            background-color: #f0f0f0;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            white-space: pre-wrap;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
    </style>
</head>
<body>
    <h1>Preview Bullets Direct Test</h1>
    <p>This page tests the preview bullets functionality directly without going through the AJAX handler.</p>
    
    <h2>Test Results</h2>
    <?php if (empty($trips)) : ?>
        <p>No trips found in the database.</p>
    <?php else : ?>
        <?php foreach ($trips as $trip) : ?>
            <div class="test-card">
                <h3><?php echo $trip->name; ?> (ID: <?php echo $trip->id; ?>)</h3>
                <p>Alias: <?php echo $trip->alias; ?></p>
                
                <?php
                $result = getPreviewBullets($trip->id);
                ?>
                
                <div class="debug-info">
                    <pre><?php echo json_encode($result, JSON_PRETTY_PRINT); ?></pre>
                </div>
                
                <?php if ($result['success']) : ?>
                    <h4 class="success">Preview Bullets Found</h4>
                    <div class="preview-bullets">
                        <?php echo $result['data']; ?>
                    </div>
                <?php else : ?>
                    <h4 class="error">Error: <?php echo $result['message']; ?></h4>
                <?php endif; ?>
            </div>
        <?php endforeach; ?>
    <?php endif; ?>
</body>
</html>
