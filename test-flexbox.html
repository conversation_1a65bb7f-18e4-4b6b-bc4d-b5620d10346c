<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flexbox Test</title>
    <style>
        /* Test 1: Basic flexbox with order */
        .test1 {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
            margin-bottom: 20px;
            border: 1px solid blue;
            padding: 10px;
        }
        .test1 .text {
            flex: 1 1 calc(60% - 15px);
            min-width: 0;
            order: 1;
            background-color: #f0f0f0;
            padding: 10px;
        }
        .test1 .image {
            flex: 0 0 40%;
            align-self: flex-start;
            order: 2;
            background-color: #e0e0e0;
            padding: 10px;
        }

        /* Test 2: Flexbox with flex-direction: row explicitly set */
        .test2 {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            gap: 30px;
            margin-bottom: 20px;
            border: 1px solid green;
            padding: 10px;
        }
        .test2 .text {
            flex: 1 1 calc(60% - 15px);
            min-width: 0;
            order: 1;
            background-color: #f0f0f0;
            padding: 10px;
        }
        .test2 .image {
            flex: 0 0 40%;
            align-self: flex-start;
            order: 2;
            background-color: #e0e0e0;
            padding: 10px;
        }

        /* Test 3: Flexbox with no flex-wrap */
        .test3 {
            display: flex;
            gap: 30px;
            margin-bottom: 20px;
            border: 1px solid red;
            padding: 10px;
        }
        .test3 .text {
            flex: 1 1 60%;
            min-width: 0;
            background-color: #f0f0f0;
            padding: 10px;
        }
        .test3 .image {
            flex: 0 0 40%;
            align-self: flex-start;
            background-color: #e0e0e0;
            padding: 10px;
        }

        /* Test 4: Flexbox with no order properties */
        .test4 {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
            margin-bottom: 20px;
            border: 1px solid purple;
            padding: 10px;
        }
        .test4 .text {
            flex: 1 1 calc(60% - 15px);
            min-width: 0;
            background-color: #f0f0f0;
            padding: 10px;
        }
        .test4 .image {
            flex: 0 0 40%;
            align-self: flex-start;
            background-color: #e0e0e0;
            padding: 10px;
        }

        /* Test 5: Flexbox with text first in HTML */
        .test5 {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
            margin-bottom: 20px;
            border: 1px solid orange;
            padding: 10px;
        }
        .test5 .text {
            flex: 1 1 calc(60% - 15px);
            min-width: 0;
            background-color: #f0f0f0;
            padding: 10px;
        }
        .test5 .image {
            flex: 0 0 40%;
            align-self: flex-start;
            background-color: #e0e0e0;
            padding: 10px;
        }

        /* Test 6: Flexbox with image first in HTML */
        .test6 {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
            margin-bottom: 20px;
            border: 1px solid brown;
            padding: 10px;
        }
        .test6 .text {
            flex: 1 1 calc(60% - 15px);
            min-width: 0;
            background-color: #f0f0f0;
            padding: 10px;
        }
        .test6 .image {
            flex: 0 0 40%;
            align-self: flex-start;
            background-color: #e0e0e0;
            padding: 10px;
        }

        /* Media query for mobile devices */
        @media (max-width: 767.98px) {
            .test1, .test2, .test3, .test4, .test5, .test6 {
                flex-direction: column;
                gap: 15px;
            }
            .test1 .text, .test1 .image,
            .test2 .text, .test2 .image,
            .test3 .text, .test3 .image,
            .test4 .text, .test4 .image,
            .test5 .text, .test5 .image,
            .test6 .text, .test6 .image {
                flex: 0 0 100%;
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <h1>Flexbox Layout Tests</h1>

    <h2>Test 1: Basic flexbox with order</h2>
    <div class="test1">
        <div class="text">
            <h3>Text Content</h3>
            <p>This is the text content that should appear on the left side of the container on desktop.</p>
        </div>
        <div class="image">
            <h3>Image Content</h3>
            <p>This represents an image that should appear on the right side of the container on desktop.</p>
        </div>
    </div>

    <h2>Test 2: Flexbox with flex-direction: row explicitly set</h2>
    <div class="test2">
        <div class="text">
            <h3>Text Content</h3>
            <p>This is the text content that should appear on the left side of the container on desktop.</p>
        </div>
        <div class="image">
            <h3>Image Content</h3>
            <p>This represents an image that should appear on the right side of the container on desktop.</p>
        </div>
    </div>

    <h2>Test 3: Flexbox with no flex-wrap</h2>
    <div class="test3">
        <div class="text">
            <h3>Text Content</h3>
            <p>This is the text content that should appear on the left side of the container on desktop.</p>
        </div>
        <div class="image">
            <h3>Image Content</h3>
            <p>This represents an image that should appear on the right side of the container on desktop.</p>
        </div>
    </div>

    <h2>Test 4: Flexbox with no order properties</h2>
    <div class="test4">
        <div class="text">
            <h3>Text Content</h3>
            <p>This is the text content that should appear on the left side of the container on desktop.</p>
        </div>
        <div class="image">
            <h3>Image Content</h3>
            <p>This represents an image that should appear on the right side of the container on desktop.</p>
        </div>
    </div>

    <h2>Test 5: Flexbox with text first in HTML</h2>
    <div class="test5">
        <div class="text">
            <h3>Text Content</h3>
            <p>This is the text content that should appear on the left side of the container on desktop.</p>
        </div>
        <div class="image">
            <h3>Image Content</h3>
            <p>This represents an image that should appear on the right side of the container on desktop.</p>
        </div>
    </div>

    <h2>Test 6: Flexbox with image first in HTML</h2>
    <div class="test6">
        <div class="image">
            <h3>Image Content</h3>
            <p>This represents an image that should appear on the right side of the container on desktop.</p>
        </div>
        <div class="text">
            <h3>Text Content</h3>
            <p>This is the text content that should appear on the left side of the container on desktop.</p>
        </div>
    </div>
</body>
</html>
